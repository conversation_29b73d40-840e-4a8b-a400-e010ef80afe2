# Point Zero

## Installation

1. Setup your environment variables by copying `.env.template` to `.env` and
   adjusting the values as needed.

   ```bash
   cp .env.template .env
   ```

2. Install the dependencies:

   ```bash
   npm install
   ```

3. Start the postgres database:

   ```bash
   docker compose up -d
   ```

4. Run the migrations:

   ```bash
   npm run migrate
   docker compose exec postgres psql -U postgres -d pointzero -c "$(cat ./better-auth_migrations/*.sql)"
   npm run create-players
   ```

5. Start the development server:

   ```bash
   npm run dev
   ```

6. Open your browser and navigate to `http://localhost:4321` to see the 
   application in action.
