{"name": "", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "db:migrate": "npx @better-auth/cli migrate", "db:generate": "npx @better-auth/cli generate", "create-players": "tsx scripts/create-players.ts"}, "dependencies": {"@amcharts/amcharts4-geodata": "^4.1.31", "@amcharts/amcharts5": "^5.13.3", "@astrojs/node": "^9.3.0", "@astrojs/react": "^4.3.0", "@astrojs/tailwind": "^6.0.2", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/ws": "^8.18.1", "astro": "^5.12.0", "better-auth": "^1.3.1", "dotenv": "^17.2.0", "drizzle-orm": "^0.44.3", "pg": "^8.16.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.17", "toastify-js": "^1.12.0", "ws": "^8.18.3"}, "devDependencies": {"@better-auth/cli": "^1.3.1", "tsx": "^4.20.3"}}