import React, { useState, useEffect, useRef, useCallback } from "react";
import VaccineButton from "./VaccineButton.jsx";
import VaccineKey from "./VaccineKey.jsx";
import DNAScreen from "./DNAScreen.jsx";
import { teamSessionManager } from "../lib/team-session-manager.js";

const VaccineMachine = () => {
    const [gridItems, setGridItems] = useState([]);
    const [buttonStates, setButtonStates] = useState({});
    const [gridSize, setGridSize] = useState({ rows: 6, cols: 6 });
    const hasGeneratedRef = useRef(false);
    
    // Vaccine key individual states for visual feedback (now synced with server)
    const [vaccineKeyStates, setVaccineKeyStates] = useState({});
    const [serverKeyStates, setServerKeyStates] = useState({});
    
    // VaccineKey game state
    const [vaccineKeyState, setVaccineKeyState] = useState({
        sessionId: null,
        keys: [],
        keyStates: {},
        pressedSequence: [],
        status: 'idle',
        isCompleted: false
    });

    // Corporate colors
    const colors = {
        primary: "#2E7D32", // Corporate Green
        secondary: "#FFB300", // Corporate Gold
        accent: "#1565C0", // Blue
        warning: "#E65100", // Orange
        success: "#388E3C", // Light Green
        info: "#0277BD", // Cyan
    };

    const buttonColors = [
        { bg: colors.primary, ring: colors.secondary },
        { bg: colors.secondary, ring: colors.primary },
        { bg: colors.accent, ring: colors.warning },
        { bg: colors.warning, ring: colors.accent },
        { bg: colors.success, ring: colors.info },
        { bg: colors.info, ring: colors.success },
    ];

    const buttonTexts = [
        "START",
        "STOP",
        "ANALYZE",
        "MIX",
        "HEAT",
        "COOL",
        "EXTRACT",
        "PURIFY",
    ];

    useEffect(() => {
        // Small delay to ensure DOM is ready
        const timer = setTimeout(calculateGridSize, 100);
        window.addEventListener("resize", calculateGridSize);
        return () => {
            clearTimeout(timer);
            window.removeEventListener("resize", calculateGridSize);
        };
    }, []);

    const calculateGridSize = () => {
        const cellSize = 80; // Minimum cell size in pixels including gap
        const padding = 64; // Total padding (32px on each side)
        
        // Get the actual container element to measure available space
        const container = document.querySelector('.vaccine-machine-container');
        if (!container) return;
        
        const containerRect = container.getBoundingClientRect();
        const availableWidth = containerRect.width - padding;
        const availableHeight = containerRect.height - padding;

        const cols = Math.floor(availableWidth / cellSize);
        const rows = Math.floor(availableHeight / cellSize);

        // Minimum grid size
        const minCols = 4;
        const minRows = 4;

        setGridSize({
            cols: Math.max(cols, minCols),
            rows: Math.max(rows, minRows),
        });
    };

    const generateRandomGrid = useCallback(() => {
        if (hasGeneratedRef.current) return;
        hasGeneratedRef.current = true;

        const items = [];
        const timestamp = Date.now();
        // const numButtons = Math.floor(Math.random() * 4) + 3; // 3-6 buttons
        const numButtons = 0; // For now, set to 0 to disable buttons
        
        // Get current session info for localStorage key
        const getCurrentSessionInfo = async () => {
            try {
                // Get session info for localStorage key
                const team = await teamSessionManager.getCurrentPlayerTeam();
                const session = teamSessionManager.sessions[team];
                return {
                    sessionId: session?.id,
                    roundId: session?.roundId
                };
            } catch (error) {
                console.error('Failed to get session info:', error);
                return { sessionId: null, roundId: null };
            }
        };

        // Calculate screen position (full width at bottom)
        const screenWidth = gridSize.cols; // Take full width
        const screenHeight = 2;
        const screenStartCol = 0; // Start from first column
        const screenStartRow = gridSize.rows - screenHeight; // Always at bottom

        // Create available positions (excluding bottom area for screen)
        const availablePositions = [];
        for (let row = 0; row < gridSize.rows; row++) {
            for (let col = 0; col < gridSize.cols; col++) {
                // Reserve bottom area for screen (full width)
                const isScreenArea =
                    row >= screenStartRow &&
                    row < screenStartRow + screenHeight;
                if (!isScreenArea) {
                    availablePositions.push({ row, col });
                }
            }
        }

        // Get or generate positions with localStorage persistence
        const getPositions = async () => {
            const sessionInfo = await getCurrentSessionInfo();
            const storageKey = `grid-positions-${sessionInfo.sessionId}-${sessionInfo.roundId}`;
            
            // Try to load saved positions
            if (sessionInfo.sessionId && sessionInfo.roundId) {
                try {
                    const savedPositions = localStorage.getItem(storageKey);
                    if (savedPositions) {
                        return JSON.parse(savedPositions);
                    }
                } catch (error) {
                    console.error('Failed to load saved positions:', error);
                }
            }
            
            // Generate new random positions
            const shuffledPositions = [...availablePositions];
            // Fisher-Yates shuffle for proper randomization
            for (let i = shuffledPositions.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffledPositions[i], shuffledPositions[j]] = [shuffledPositions[j], shuffledPositions[i]];
            }
            
            // Save positions to localStorage
            if (sessionInfo.sessionId && sessionInfo.roundId) {
                try {
                    localStorage.setItem(storageKey, JSON.stringify(shuffledPositions));
                } catch (error) {
                    console.error('Failed to save positions:', error);
                }
            }
            
            return shuffledPositions;
        };
        
        // Get positions (either saved or newly generated)
        getPositions().then(shuffledPositions => {
            let positionIndex = 0;

            // Generate buttons
            for (
                let i = 0;
                i < numButtons && positionIndex < shuffledPositions.length;
                i++
            ) {
                const colorSet =
                    buttonColors[Math.floor(Math.random() * buttonColors.length)];
                const position = shuffledPositions[positionIndex++];
                items.push({
                    id: `btn-${timestamp}-${i}`,
                    type: "button",
                    gridRow: position.row,
                    gridCol: position.col,
                    text: buttonTexts[
                        Math.floor(Math.random() * buttonTexts.length)
                    ],
                    bgColor: colorSet.bg,
                    ringColor: colorSet.ring,
                });
            }

            // Generate keys from vaccineKeyState.keys received from team session manager
            for (
                let i = 0;
                i < vaccineKeyState.keys.length && positionIndex < shuffledPositions.length;
                i++
            ) {
                const position = shuffledPositions[positionIndex++];
                const keyData = vaccineKeyState.keys[i];
                const keyItemId = `key-${timestamp}-${i}`;
                items.push({
                    id: keyItemId,
                    type: "key",
                    gridRow: position.row,
                    gridCol: position.col,
                    symbol: keyData.symbol,
                    keyId: keyData.id, // Store the key ID from API
                });
            }

            // Add screen in the bottom area
            items.push({
                id: `screen-${timestamp}`,
                type: "screen",
                gridRow: screenStartRow,
                gridCol: screenStartCol,
                gridRowSpan: screenHeight,
                gridColSpan: screenWidth,
            });

            setGridItems(items);
            setButtonStates({});
        }).catch(error => {
            console.error('Failed to generate grid:', error);
            // Fallback to old random generation
            const fallbackItems = [];
            setGridItems(fallbackItems);
        });
    }, [gridSize, vaccineKeyState.keys]);

    useEffect(() => {
        hasGeneratedRef.current = false;
        generateRandomGrid();
    }, [gridSize, generateRandomGrid]);
    

    // Initialize team session manager
    useEffect(() => {
        let isMounted = true;

        const initializeSessionManager = async () => {
            try {
                // Set up listener for vaccine key updates
                teamSessionManager.onVaccineKeyUpdate((keyState) => {
                    if (!isMounted) return;
                    setVaccineKeyState(keyState);
                    
                    // Sync server key states for persistence
                    if (keyState.keyStates) {
                        setServerKeyStates(keyState.keyStates);
                    }
                    
                    // Reset local key states when new round starts or session becomes idle
                    if (keyState.status === 'active' && !keyState.keyStates) {
                        setVaccineKeyStates({});
                        setServerKeyStates({});
                    } else if (keyState.status === 'idle') {
                        // Clear all states when session is closed
                        setVaccineKeyStates({});
                        setServerKeyStates({});
                        setGridItems(prevItems => prevItems.filter(item => item.type === 'screen'));
                    }
                });

                // Set up listener for session status changes
                teamSessionManager.onSessionStatusChange((sessionStatus) => {
                    if (!isMounted) return;
                    
                    // Clear components when session becomes inactive
                    const currentTeam = teamSessionManager.getCurrentPlayerTeam();
                    if (currentTeam && sessionStatus[currentTeam] && !sessionStatus[currentTeam].active) {
                        setVaccineKeyStates({});
                        setServerKeyStates({});
                        setVaccineKeyState({
                            sessionId: null,
                            keys: [],
                            keyStates: {},
                            pressedSequence: [],
                            status: 'idle',
                            isCompleted: false
                        });
                        setGridItems(prevItems => prevItems.filter(item => item.type === 'screen'));
                        hasGeneratedRef.current = false; // Allow regeneration when new session starts
                    }
                });

                // Initialize the team session manager
                await teamSessionManager.initialize();

            } catch (error) {
                console.error("Team session manager setup failed:", error);
            }
        };

        initializeSessionManager();

        return () => {
            isMounted = false;
        };
    }, []);

    // Handle validation results and grid-dependent key state updates
    useEffect(() => {
        let isMounted = true;

        // Set up validation result listener for immediate feedback
        const unsubscribe = teamSessionManager.onValidationResult((result) => {
            if (!isMounted) return;
            
            // Handle vaccine key validation results
            if (result.gameType === 'vaccine_key') {
                // Skip intermediate results - keyStates are now handled by team session manager
                // Only process final results (when correctAnswer is provided for failures)
                if (result.succeeded || result.correctAnswer) {
                    if (result.succeeded) {
                        // Set all keys to success (blue) state - persist until next round
                        const successStates = {};
                        gridItems.forEach(item => {
                            if (item.type === 'key') {
                                successStates[item.id] = 'success';
                            }
                        });
                        setVaccineKeyStates(successStates);
                    } else {
                        // Set all keys to error (red) state
                        const errorStates = {};
                        gridItems.forEach(item => {
                            if (item.type === 'key') {
                                errorStates[item.id] = 'error';
                            }
                        });
                        setVaccineKeyStates(errorStates);
                        
                        // Reset error state after delay for retry
                        setTimeout(() => {
                            if (isMounted) {
                                setVaccineKeyStates({});
                            }
                        }, 4000);
                    }
                }
            } else if (result.gameType === 'dna' && !result.succeeded) {
                // If DNA failed, show failure feedback on vaccine keys too (round ends)
                const errorStates = {};
                gridItems.forEach(item => {
                    if (item.type === 'key') {
                        errorStates[item.id] = 'error';
                    }
                });
                setVaccineKeyStates(errorStates);
                
                // Reset error state after delay
                setTimeout(() => {
                    if (isMounted) {
                        setVaccineKeyStates({});
                    }
                }, 4000);
            }
        });

        return () => {
            isMounted = false;
            if (unsubscribe) unsubscribe();
        };
    }, [gridItems]);

    // Convert server key states to component key states when gridItems or serverKeyStates change
    useEffect(() => {
        if (Object.keys(serverKeyStates).length > 0 && gridItems.length > 0) {
            const componentKeyStates = {};
            gridItems.forEach(item => {
                if (item.type === 'key' && item.keyId && serverKeyStates[item.keyId]) {
                    componentKeyStates[item.id] = serverKeyStates[item.keyId];
                }
            });
            if (Object.keys(componentKeyStates).length > 0) {
                setVaccineKeyStates(componentKeyStates);
            }
        }
    }, [gridItems, serverKeyStates]);

    const handleButtonStateChange = (buttonId, state) => {
        setButtonStates((prev) => ({ ...prev, [buttonId]: state }));
    };

    const handleKeyPress = (symbol) => {
        // Comprehensive validation before allowing key press
        if (vaccineKeyState.status !== 'active') {
            return;
        }

        // Find the key item and its keyId
        const keyItem = gridItems.find(item => item.type === 'key' && item.symbol === symbol);
        if (!keyItem) {
            console.error('Key press ignored: Key item not found for symbol:', symbol);
            return;
        }
        
        if (!keyItem.keyId) {
            console.error('Key press ignored: Key item missing keyId:', keyItem);
            return;
        }
        
        // Check if key is already in error state (should be disabled)
        const keyState = vaccineKeyStates[keyItem.id];
        if (keyState === 'error') {
            return;
        }
        
        // Send key press to API for validation via team session manager
        const success = teamSessionManager.submitVaccineKeyAnswer({
            keyId: keyItem.keyId,
            symbol: symbol
        });
        
        if (!success) {
            console.error('Failed to submit vaccine key answer - no active session');
        }
    };

    return (
        <div
            className="vaccine-machine-container w-full h-full relative"
            style={{
                background: "linear-gradient(145deg, #1a202c, #2d3748)",
            }}
        >
            {/* Grid Container */}
            <div className="h-full p-4 md:p-8">
                <div
                    className="grid gap-2 md:gap-4 h-full mx-auto"
                    style={{
                        gridTemplateColumns: `repeat(${gridSize.cols}, minmax(0, 1fr))`,
                        gridTemplateRows: `repeat(${gridSize.rows}, minmax(0, 1fr))`,
                    }}
                >
                    {gridItems.map((item) => {
                        const gridStyle = {
                            gridRow: item.gridRowSpan
                                ? `${item.gridRow + 1} / span ${
                                      item.gridRowSpan
                                  }`
                                : item.gridRow + 1,
                            gridColumn: item.gridColSpan
                                ? `${item.gridCol + 1} / span ${
                                      item.gridColSpan
                                  }`
                                : item.gridCol + 1,
                            zIndex: 10,
                        };

                        switch (item.type) {
                            case "button":
                                return (
                                    <div
                                        key={item.id}
                                        style={gridStyle}
                                        className="flex items-center justify-center min-w-0 min-h-0"
                                    >
                                        <VaccineButton
                                            item={item}
                                            state={
                                                buttonStates[item.id] ||
                                                "normal"
                                            }
                                            onStateChange={
                                                handleButtonStateChange
                                            }
                                        />
                                    </div>
                                );
                            case "key":
                                return (
                                    <div
                                        key={item.id}
                                        style={gridStyle}
                                        className="flex items-center justify-center min-w-0 min-h-0"
                                    >
                                        <VaccineKey
                                            item={item}
                                            onPress={handleKeyPress}
                                            state={vaccineKeyStates[item.id] || 'normal'}
                                        />
                                    </div>
                                );
                            case "screen":
                                return (
                                    <div
                                        key="dna-screen" // Stable key to prevent unmounting
                                        style={gridStyle}
                                        className="flex items-center justify-center min-w-0 min-h-0"
                                    >
                                        <DNAScreen />
                                    </div>
                                );
                            default:
                                return null;
                        }
                    })}
                </div>
            </div>
        </div>
    );
};

export default VaccineMachine;
