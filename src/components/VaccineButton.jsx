import React, { useRef } from 'react';

const VaccineButton = ({ item, state, onStateChange }) => {
  const holdTimeouts = useRef({});
  const isPressed = state === 'pressed';
  const isHeld = state === 'held';
  const showRingLight = isHeld;

  const handleButtonPress = () => {
    onStateChange(item.id, 'pressed');
    
    // Clear any existing timeout for this button
    if (holdTimeouts.current[item.id]) {
      clearTimeout(holdTimeouts.current[item.id]);
    }
    
    // Set timeout for hold detection
    holdTimeouts.current[item.id] = setTimeout(() => {
      onStateChange(item.id, 'held');
    }, 1000);
  };

  const handleButtonRelease = () => {
    if (holdTimeouts.current[item.id]) {
      clearTimeout(holdTimeouts.current[item.id]);
    }
    
    setTimeout(() => {
      onStateChange(item.id, 'normal');
    }, 200);
  };

  return (
    <div
      className="relative select-none w-full h-full flex items-center justify-center"
      onMouseDown={handleButtonPress}
      onMouseUp={handleButtonRelease}
      onMouseLeave={handleButtonRelease}
      onTouchStart={handleButtonPress}
      onTouchEnd={handleButtonRelease}
    >
      <div className="relative w-full h-full flex items-center justify-center">
        {/* Container that maintains square aspect ratio */}
        <div className="relative w-full" style={{ aspectRatio: '1' }}>
          {/* Ring light with 3D effect */}
          <div 
            className={`absolute inset-0 rounded-full transition-all duration-300 ${
              isHeld ? 'animate-pulse' : ''
            }`}
            style={{
              border: `4px solid ${showRingLight ? item.ringColor : '#4B5563'}`,
              boxShadow: showRingLight 
                ? `0 0 20px ${item.ringColor}, inset 0 2px 4px rgba(0,0,0,0.3), 0 4px 8px rgba(0,0,0,0.2)`
                : 'inset 0 2px 4px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.1)',
              background: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.2), transparent 50%)`
            }}
          />
          {/* Button with 3D depth */}
          <div
            className={`absolute inset-2 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-pointer transition-all duration-150 ${
              isPressed ? 'scale-95' : 'scale-100'
            }`}
            style={{
              background: `linear-gradient(145deg, ${item.bgColor}, ${item.bgColor}dd)`,
              boxShadow: isPressed
                ? `inset 0 4px 8px rgba(0,0,0,0.3), inset 0 -2px 4px rgba(255,255,255,0.1)`
                : `0 6px 12px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)`,
              transform: isPressed ? 'scale(0.95) translateY(3px)' : 'scale(1) translateY(0px)',
            }}
          >
            <span className="text-center leading-tight">{item.text}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VaccineButton;