import React, { useEffect, useRef } from 'react';
import Scoreboard from './Scoreboard.jsx';

const WorldMap = () => {
  const chartRef = useRef(null);
  const rootRef = useRef(null);

  useEffect(() => {
    let root;
    let interval;

    const loadChart = async () => {
      try {
        // Dynamic imports for amCharts5
        const [
          { Root, color, Template, Container, Circle, Label, Bullet, p50 },
          { MapChart, MapPolygonSeries, MapPointSeries, MapLineSeries },
          am4geodata_worldLow,
          am5themes_Animated
        ] = await Promise.all([
          import("@amcharts/amcharts5"),
          import("@amcharts/amcharts5/map"),
          import("@amcharts/amcharts4-geodata/worldLow"),
          import("@amcharts/amcharts5/themes/Animated")
        ]);

        // Helper function to get coordinates for cities
        function getCityCoords(cityId) {
          const coords = {
            "PE": [-77.0, -12.0],   // Lima
            "US": [-77.0, 38.9],    // Washington
            "BR": [-47.9, -15.8],   // Brasília
            "FR": [2.3, 48.9],      // Paris
            "GR": [23.7, 37.9],     // Athens
            "ZA": [28.2, -25.7],    // Pretoria
            "CN": [116.4, 39.9],    // Pékin
            "IN": [77.2, 28.6],     // New Delhi
            "AU": [149.1, -35.3]    // Canberra
          };
          return coords[cityId] || [0, 0];
        }

        // Helper function to get regional colors
        function getRegionColor(cityId) {
          const regions = {
            // Americas - Red
            "PE": 0xff0000,   // Lima
            "US": 0xff0000,   // Washington  
            "BR": 0xff0000,   // Brasília
            // Europe/Africa - Green
            "FR": 0x00ff00,   // Paris
            "GR": 0x00ff00,   // Athens
            "ZA": 0x00ff00,   // Pretoria
            // Asia/Australia - Blue
            "CN": 0x0000ff,   // Pékin
            "IN": 0x0000ff,   // New Delhi
            "AU": 0x0000ff    // Canberra
          };
          return regions[cityId] || 0xffffff;
        }

        // Data for the map - using exact coordinates
        const data = [
          {
            id: "PE",
            name: "Lima",
            value: 100,
            longitude: getCityCoords("PE")[0],
            latitude: getCityCoords("PE")[1]
          },
          {
            id: "US", 
            name: "Washington",
            value: 100,
            longitude: getCityCoords("US")[0],
            latitude: getCityCoords("US")[1]
          },
          {
            id: "BR",
            name: "Brasília", 
            value: 100,
            longitude: getCityCoords("BR")[0],
            latitude: getCityCoords("BR")[1]
          },
          {
            id: "FR",
            name: "Paris",
            value: 100,
            longitude: getCityCoords("FR")[0],
            latitude: getCityCoords("FR")[1]
          },
          {
            id: "GR",
            name: "Athènes",
            value: 100,
            longitude: getCityCoords("GR")[0],
            latitude: getCityCoords("GR")[1]
          },
          {
            id: "ZA",
            name: "Pretoria",
            value: 100,
            longitude: getCityCoords("ZA")[0],
            latitude: getCityCoords("ZA")[1]
          },
          {
            id: "CN",
            name: "Pékin",
            value: 100,
            longitude: getCityCoords("CN")[0],
            latitude: getCityCoords("CN")[1]
          },
          {
            id: "IN",
            name: "New Delhi", 
            value: 100,
            longitude: getCityCoords("IN")[0],
            latitude: getCityCoords("IN")[1]
          },
          {
            id: "AU",
            name: "Canberra",
            value: 100,
            longitude: getCityCoords("AU")[0],
            latitude: getCityCoords("AU")[1]
          }
        ];

        // Regional and inter-cluster connections
        const connections = [
          // Americas connections
          { from: "PE", to: "US" },    // Lima <-> Washington
          { from: "US", to: "PE" },    
          { from: "US", to: "BR" },    // Washington <-> Brasília
          { from: "BR", to: "US" },
          { from: "BR", to: "PE" },    // Brasília <-> Lima
          { from: "PE", to: "BR" },
          
          // Europe/Africa connections  
          { from: "FR", to: "GR" },    // Paris <-> Athens
          { from: "GR", to: "FR" },
          { from: "GR", to: "ZA" },    // Athens <-> Pretoria
          { from: "ZA", to: "GR" },
          { from: "ZA", to: "FR" },    // Pretoria <-> Paris  
          { from: "FR", to: "ZA" },
          
          // Asia/Australia connections
          { from: "CN", to: "IN" },    // Pékin <-> New Delhi
          { from: "IN", to: "CN" },
          { from: "IN", to: "AU" },    // New Delhi <-> Canberra
          { from: "AU", to: "IN" },
          { from: "AU", to: "CN" },    // Canberra <-> Pékin
          { from: "CN", to: "AU" },
          
          // Inter-cluster connections
          { from: "US", to: "CN" },    // Washington <-> Pékin
          { from: "CN", to: "US" },
          { from: "PE", to: "AU" },    // Lima <-> Canberra
          { from: "AU", to: "PE" },
          { from: "ZA", to: "BR" },    // Pretoria <-> Brasília
          { from: "BR", to: "ZA" },
          { from: "GR", to: "IN" },    // Athènes <-> New Delhi
          { from: "IN", to: "GR" }
        ];

        // Create root element
        root = Root.new(chartRef.current);
        rootRef.current = root;

        // Set themes
        root.setThemes([am5themes_Animated.default.new(root)]);

        // Create the map chart
        const chart = root.container.children.push(
          MapChart.new(root, {
            panX: "none",
            panY: "none",
            wheelX: "none",
            wheelY: "none"
          })
        );

        // Create main polygon series for world map
        const polygonSeries = chart.series.push(
          MapPolygonSeries.new(root, {
            geoJSON: am4geodata_worldLow.default,
            exclude: ["AQ"]
          })
        );

        // Create line series for connections FIRST (so they appear under bubbles)
        const lineSeries = chart.series.push(
          MapLineSeries.new(root, {
            valueField: "value",
            calculateAggregates: true
          })
        );

        // Style the connection lines - white color
        lineSeries.mapLines.template.setAll({
          stroke: color(0xffffff),
          strokeWidth: 2,
          strokeOpacity: 0.8,
          strokeDasharray: [4, 4]
        });

        // Set connection data
        const connectionData = connections.map(conn => ({
          geometry: {
            type: "LineString",
            coordinates: [
              [getCityCoords(conn.from)[0], getCityCoords(conn.from)[1]],
              [getCityCoords(conn.to)[0], getCityCoords(conn.to)[1]]
            ]
          },
          value: 1
        }));

        lineSeries.data.setAll(connectionData);

        // Create point series for bubbles (AFTER lines so they appear on top)
        const bubbleSeries = chart.series.push(
          MapPointSeries.new(root, {
            valueField: "value",
            calculateAggregates: true,
            longitudeField: "longitude",
            latitudeField: "latitude"
          })
        );

        // Create circle template
        const circleTemplate = Template.new({});

        // Add main circle bullets
        bubbleSeries.bullets.push(function(root, series, dataItem) {
          const container = Container.new(root, {});
          const cityId = dataItem.dataContext.id;
          const regionColor = getRegionColor(cityId);

          const circle = container.children.push(
            Circle.new(root, {
              radius: 20,
              fillOpacity: 0.7,
              fill: color(regionColor),
              cursorOverStyle: "pointer",
              tooltipText: `{name}: [bold]{value}[/]`
            }, circleTemplate)
          );

          const countryLabel = container.children.push(
            Label.new(root, {
              text: "{name}",
              paddingLeft: 5,
              populateText: true,
              fontWeight: "bold",
              fontSize: 13,
              centerY: p50,
              fill: color(0xffffff)
            })
          );

          circle.on("radius", function(radius) {
            countryLabel.set("x", radius);
          });

          return Bullet.new(root, {
            sprite: container,
            dynamic: true
          });
        });

        // Add value label bullets
        bubbleSeries.bullets.push(function(root, series, dataItem) {
          return Bullet.new(root, {
            sprite: Label.new(root, {
              text: "{value.formatNumber('#.')}",
              fill: color(0xffffff),
              populateText: true,
              centerX: p50,
              centerY: p50,
              textAlign: "center"
            }),
            dynamic: true
          });
        });

        // Set heat rules for bubble sizing
        bubbleSeries.set("heatRules", [
          {
            target: circleTemplate,
            dataField: "value",
            min: 10,
            max: 50,
            minValue: 0,
            maxValue: 100,
            key: "radius"
          }
        ]);

        // Set initial data
        bubbleSeries.data.setAll(data);

        // Animation function
        function updateData() {
          for (let i = 0; i < bubbleSeries.dataItems.length; i++) {
            bubbleSeries.data.setIndex(i, {
              value: Math.round(Math.random() * 100),
              id: data[i].id,
              name: data[i].name,
              longitude: data[i].longitude,
              latitude: data[i].latitude
            });
          }
        }

        // Initial update and interval
        updateData();
        interval = setInterval(updateData, 2000);

      } catch (error) {
        console.error('Error loading amCharts:', error);
      }
    };

    loadChart();

    // Cleanup function
    return () => {
      if (interval) clearInterval(interval);
      if (root) root.dispose();
    };
  }, []);

  return (
    <div className="w-full h-full bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 relative">
      <div className="h-full container flex flex-col mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Carte des Bastions du Monde
          </h1>
          <p className="text-gray-300 text-lg">
            Évolution en temps réel de la pandémie
          </p>
        </div>

        <div className="h-full bg-black/30 backdrop-blur-sm rounded-lg p-6 border border-blue-500/20">
          <Scoreboard />
          <div
            ref={chartRef}
            className="w-full h-full"
          />
        </div>
      </div>
    </div>
  );
}

export default WorldMap;