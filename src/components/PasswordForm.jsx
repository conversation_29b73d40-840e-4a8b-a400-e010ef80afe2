import { useState, useEffect } from 'react';
import { showSuccessToast, showErrorToast } from '../lib/toast';
import { authClient } from '../lib/auth-client';
import PasswordChangeForm from './PasswordChangeForm';

export default function PasswordForm({ 
  userName, 
  elements, 
  onBack
}) {
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isBackPressed, setIsBackPressed] = useState(false);
  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [authenticatedUser, setAuthenticatedUser] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [isFirstLogin, setIsFirstLogin] = useState(true);
  const [hasCheckedLoginStatus, setHasCheckedLoginStatus] = useState(false);

  // Special user restrictions
  const isPersonne = userName?.toLowerCase() === 'personne';

  // Get redirect URL from query parameters with security validation
  const getRedirectUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const redirect = urlParams.get('redirect');
    
    if (!redirect) return '/';
    
    const decodedRedirect = decodeURIComponent(redirect);
    
    // Security: Only allow internal redirects (starting with /)
    // Prevent open redirects to external domains
    if (!decodedRedirect.startsWith('/') || decodedRedirect.startsWith('//')) {
      return '/';
    }
    
    return decodedRedirect;
  };

  // Check user login status when component loads
  useEffect(() => {
    const checkUserStatus = async () => {
      if (!userName || hasCheckedLoginStatus) return;

      try {
        const response = await fetch('/api/auth/check-user-status', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userName }),
        });

        if (response.ok) {
          const data = await response.json();
          setIsFirstLogin(data.isFirstLogin);
          setHasCheckedLoginStatus(true);
        }
      } catch (error) {
        console.error('Failed to check user status:', error);
        // Default to first login if check fails
        setIsFirstLogin(true);
        setHasCheckedLoginStatus(true);
      }
    };

    checkUserStatus();
  }, [userName, hasCheckedLoginStatus]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!password.trim()) return;

    setIsLoading(true);
    try {
      const email = `${userName.toLowerCase()}@vertven.com`;

      const { data, error } = await authClient.signIn.email({
        email,
        password: password.trim(),
      });

      if (data && data.user) {
        // Fetch full user data with additional fields via API
        try {
          const userResponse = await fetch('/api/auth/get-user', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${data.session?.token}`,
            },
          });
          
          if (userResponse.ok) {
            const fullUserData = await userResponse.json();
            
            // Check if user must change password (but not for PERSONNE)
            if (fullUserData.mustChangePassword && !isPersonne) {
              showSuccessToast(`Welcome ${userName}! Please set a new password.`);
              setAuthenticatedUser(fullUserData);
              setShowPasswordChange(true);
              setIsFirstLogin(false); // No longer first login after this point
              return; // Don't redirect
            } else {
              setIsFirstLogin(false); // User has already changed password
            }
          }
        } catch (fetchError) {
          console.error('Failed to fetch full user data:', fetchError);
        }
        
        showSuccessToast(`🎉 Welcome, ${userName}! Access Granted.`);
        window.location.href = getRedirectUrl();
      } else {
        showErrorToast(error?.message || 'Invalid password');
      }
    } catch (error) {
      console.error('Error:', error);
      showErrorToast('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChanged = () => {
    // After password is changed successfully, redirect to intended page
    window.location.href = getRedirectUrl();
  };

  // If showing password change form, render that instead
  if (showPasswordChange && authenticatedUser) {
    return (
      <PasswordChangeForm 
        user={authenticatedUser}
        onPasswordChanged={handlePasswordChanged}
      />
    );
  }

  return (
    <>
      <button
        type="button"
        onClick={onBack}
        className="mb-5 px-5 py-2 rounded-lg text-sm font-bold cursor-pointer transition-all select-none"
        style={{
          background: 'linear-gradient(145deg, #f59e0b, #d97706)',
          color: '#fff',
          border: 'none',
          boxShadow: isBackPressed
            ? 'inset 0 2px 4px rgba(0,0,0,0.4)'
            : '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)',
          transform: isBackPressed ? 'translateY(1px)' : 'translateY(0px)'
        }}
        onMouseDown={() => setIsBackPressed(true)}
        onMouseUp={() => setIsBackPressed(false)}
        onMouseLeave={() => setIsBackPressed(false)}
        onTouchStart={() => setIsBackPressed(true)}
        onTouchEnd={() => setIsBackPressed(false)}
      >
        ← Back
      </button>
      
      <div className="flex flex-wrap gap-2 justify-center my-5 min-h-20">
        {elements.map((symbol, index) => (
          <div
            key={index}
            className="relative w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-lg"
            style={{
              background: 'linear-gradient(145deg, #2563eb, #1d4ed8)',
              boxShadow: '0 4px 8px rgba(0,0,0,0.3), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
              animation: `elementAppear 0.5s ease-out ${index * 0.1}s both`
            }}
          >
            {symbol.toUpperCase()}
          </div>
        ))}
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="mb-6 relative">
          <input
            type={isFirstLogin || isPersonne ? "text" : (showPassword ? "text" : "password")}
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            maxLength={20}
            required
            disabled={isLoading}
            className="w-full p-4 rounded-lg text-base font-mono tracking-wider text-center transition-all"
            style={{
              background: 'linear-gradient(145deg, #000000, #1a1a1a)',
              color: '#4ade80',
              border: '2px solid #374151',
              boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.5), inset 0 -2px 4px rgba(255,255,255,0.1)',
              paddingRight: (!isFirstLogin && !isPersonne) ? '50px' : '16px'
            }}
            onFocus={(e) => {
              e.target.style.border = '2px solid #2563eb';
            }}
            onBlur={(e) => {
              e.target.style.border = '2px solid #374151';
            }}
          />
          {!isFirstLogin && !isPersonne && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px'
              }}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          )}
        </div>
        <button
          type="submit"
          disabled={isLoading || password.trim().length === 0}
          className="w-full p-4 rounded-lg text-base font-bold cursor-pointer transition-all select-none"
          style={{
            background: (isLoading || password.trim().length === 0) 
              ? 'linear-gradient(145deg, #6b7280, #4b5563)'
              : 'linear-gradient(145deg, #16a34a, #15803d)',
            color: '#fff',
            border: 'none',
            boxShadow: isSubmitPressed
              ? 'inset 0 4px 8px rgba(0,0,0,0.4)'
              : '0 6px 12px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
            transform: isSubmitPressed ? 'translateY(3px)' : 'translateY(0px)',
            opacity: (isLoading || password.trim().length === 0) ? 0.6 : 1,
            cursor: (isLoading || password.trim().length === 0) ? 'not-allowed' : 'pointer'
          }}
          onMouseDown={() => !isLoading && password.trim().length > 0 && setIsSubmitPressed(true)}
          onMouseUp={() => setIsSubmitPressed(false)}
          onMouseLeave={() => setIsSubmitPressed(false)}
          onTouchStart={() => !isLoading && password.trim().length > 0 && setIsSubmitPressed(true)}
          onTouchEnd={() => setIsSubmitPressed(false)}
        >
          {isLoading ? 'Verifying...' : 'Login'}
        </button>
      </form>
      
      <style>{`
        @keyframes elementAppear {
          from {
            opacity: 0;
            transform: scale(0.5);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </>
  );
}
