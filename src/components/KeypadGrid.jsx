import { useState } from 'react';

export default function KeypadGrid({ onKeyPress, disabled = false }) {
    const [allPressed, setAllPressed] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);

    const handleKeyClick = (keyIndex, e) => {
        e.preventDefault();
        e.stopPropagation();
        
        if (disabled || isProcessing) return;
        
        // Prevent multiple rapid clicks
        setIsProcessing(true);
        
        // Light up all keys
        setAllPressed(true);
        setTimeout(() => setAllPressed(false), 150);
        
        // Call the parent's key press handler
        onKeyPress();
        
        // Re-enable after a short delay
        setTimeout(() => setIsProcessing(false), 100);
    };

    const keys = Array.from({ length: 10 }, (_, i) => i);

    return (
        <div className="keypad">
            {keys.map((keyIndex) => (
                <div
                    key={keyIndex}
                    className={`key ${allPressed ? 'pressed' : ''}`}
                    onClick={(e) => handleKeyClick(keyIndex, e)}
                    onTouchStart={(e) => e.preventDefault()}
                    style={{
                        pointerEvents: disabled || isProcessing ? 'none' : 'auto',
                        cursor: disabled ? 'default' : 'pointer'
                    }}
                >
                </div>
            ))}
        </div>
    );
}