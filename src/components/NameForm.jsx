import { useState } from 'react';
import { showErrorToast } from '../lib/toast';

export default function NameForm({ onSubmit }) {
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!name.trim()) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/auth/getElements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim()
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        onSubmit(name.trim(), data.elements);
      } else {
        showErrorToast(data.error || 'Invalid name');
      }
    } catch (error) {
      console.error('Error:', error);
      showErrorToast('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-6">
        <input
          type="text"
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="Enter your name"
          maxLength={20}
          required
          disabled={isLoading}
          className="w-full p-4 rounded-lg text-base transition-all font-mono"
          style={{
            background: 'linear-gradient(145deg, #000000, #1a1a1a)',
            color: '#4ade80',
            border: '2px solid #374151',
            boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.5), inset 0 -2px 4px rgba(255,255,255,0.1)'
          }}
          onFocus={(e) => {
            e.target.style.border = '2px solid #2563eb';
          }}
          onBlur={(e) => {
            e.target.style.border = '2px solid #374151';
          }}
        />
      </div>
      <button
        type="submit"
        disabled={isLoading || name.trim().length === 0}
        className="w-full p-4 rounded-lg text-base font-bold cursor-pointer transition-all select-none"
        style={{
          background: (isLoading || name.trim().length === 0) 
            ? 'linear-gradient(145deg, #6b7280, #4b5563)'
            : 'linear-gradient(145deg, #2563eb, #1d4ed8)',
          color: '#fff',
          border: 'none',
          boxShadow: isPressed
            ? 'inset 0 4px 8px rgba(0,0,0,0.4)'
            : '0 6px 12px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
          transform: isPressed ? 'translateY(3px)' : 'translateY(0px)',
          opacity: (isLoading || name.trim().length === 0) ? 0.6 : 1,
          cursor: (isLoading || name.trim().length === 0) ? 'not-allowed' : 'pointer'
        }}
        onMouseDown={() => !isLoading && name.trim().length > 0 && setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        onMouseLeave={() => setIsPressed(false)}
        onTouchStart={() => !isLoading && name.trim().length > 0 && setIsPressed(true)}
        onTouchEnd={() => setIsPressed(false)}
      >
        {isLoading ? 'Processing...' : 'Submit'}
      </button>
    </form>
  );
}
