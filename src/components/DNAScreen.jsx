import React, { useState, useEffect } from 'react';
import { teamSessionManager } from '../lib/team-session-manager.js';

const DNAScreen = () => {
  const [currentInput, setCurrentInput] = useState('');
  const [showInputScreen, setShowInputScreen] = useState(false);
  // Server-controlled feedback state
  const [serverFeedback, setServerFeedback] = useState(null);
  const [dnaState, setDnaState] = useState({
    sessionId: null,
    sequence: [],
    status: 'idle',
    isCompleted: false
  });

  useEffect(() => {
    let isMounted = true;

    const initializeDnaManager = async () => {
      try {
        // Set up general game state listener for server-controlled feedback
        teamSessionManager.onGameStateChange((gameState) => {
          if (!isMounted) return;
          
          // Update DNA state
          setDnaState(gameState.dnaScreen);
          
          // Handle server-controlled feedback
          if (gameState.dnaScreen.feedback) {
            setServerFeedback(gameState.dnaScreen.feedback);
            setCurrentInput('');
            setShowInputScreen(false);
          } else {
            setServerFeedback(null);
          }
          
          // Reset input screen when new round starts
          if (gameState.dnaScreen.status === 'active' && !gameState.dnaScreen.feedback) {
            setCurrentInput('');
            setShowInputScreen(false);
          }

          // Remove the sequence if the session is completed
          if (gameState.dnaScreen.status === 'idle' && !gameState.dnaScreen.feedback) {
            setDnaState((prevState) => ({
              ...prevState,
              sequence: [],
            }));
          }
        });
      } catch (error) {
        console.error("Team session manager setup failed:", error);
      }
    };

    initializeDnaManager();

    return () => { isMounted = false; };
  }, []); // Empty dependency array to avoid stale closures

  const handleGapClick = (index) => {
    // Comprehensive validation before allowing interaction
    if (dnaState.status !== 'active') {
      return;
    }
    
    if (serverFeedback?.show) {
      return;
    }
    
    if (showInputScreen) {
      return;
    }
    
    const sequenceArray = Array.isArray(dnaState.sequence) ? dnaState.sequence : dnaState.sequence.split(' ');
    if (sequenceArray[index] === '?') {
      setShowInputScreen(true);
    }
  };

  const handleNucleotideClick = (nucleotide) => {
    // Validate state before accepting nucleotide input
    if (dnaState.status !== 'active') {
      return;
    }
    
    if (serverFeedback?.show) {
      return;
    }
    
    if (!showInputScreen) {
      return;
    }
    
    // Validate nucleotide
    if (!['A', 'C', 'G', 'T'].includes(nucleotide)) {
      console.error('Invalid nucleotide:', nucleotide);
      return;
    }
    
    setCurrentInput(nucleotide);
  };

  const handleSubmit = () => {
    // Comprehensive validation before submission
    if (!currentInput) {
      return;
    }
    
    if (dnaState.status !== 'active') {
      return;
    }
    
    if (serverFeedback?.show) {
      return;
    }
    
    if (!['A', 'C', 'G', 'T'].includes(currentInput)) {
      console.error('Invalid nucleotide for submission:', currentInput);
      return;
    }
    
    const success = teamSessionManager.submitDnaAnswer(currentInput);
    if (!success) {
      console.error('Failed to submit DNA answer - no active session');
    }
  };

  const handleBack = () => {
    setShowInputScreen(false);
    setCurrentInput('');
  };

  return (
    <div
      className="w-full h-full rounded-lg p-4 relative"
      style={{
        background: 'linear-gradient(145deg, #4a5568, #2d3748)',
        boxShadow: '0 4px 8px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.3), inset 0 1px 2px rgba(255,255,255,0.1)'
      }}
    >
      {/* Server-Controlled Feedback Overlay */}
      {serverFeedback && serverFeedback.show && (
        <div className="absolute inset-2 bg-black bg-opacity-90 rounded flex items-center justify-center z-20">
          <div className="text-center">
            <div className={`text-2xl font-bold mb-2 ${serverFeedback.correct ? 'text-green-400' : 'text-red-400'}`}>
              {serverFeedback.correct ? '✓ CORRECT' : '✗ INCORRECT'}
            </div>
            <div className="text-white mb-2">
              {serverFeedback.correct ? `+${serverFeedback.points} points` : `${serverFeedback.points} point`}
            </div>
            {serverFeedback.message && (
              <div className="text-yellow-400 text-sm">
                {serverFeedback.message}
              </div>
            )}
          </div>
        </div>
      )}

      <div className="h-full">
        {!showInputScreen ? (
          // DNA Display Screen
          <div 
            className="w-full h-full rounded p-2"
            style={{
              background: 'linear-gradient(145deg, #000000, #1a1a1a)',
              boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.5), inset 0 -2px 4px rgba(255,255,255,0.1)'
            }}
          >
            <div className="h-full flex flex-col">
              <div className="flex-1 flex items-center justify-center">
                <div className="text-green-400 font-mono text-center">
                  <div className="flex flex-wrap justify-center gap-2">
                    {dnaState.sequence && dnaState.sequence.length > 0 ? (Array.isArray(dnaState.sequence) ? dnaState.sequence : dnaState.sequence.split(' ')).map((part, index) => (
                      <span
                        key={index}
                        className={`px-3 py-4 rounded transition-colors text-lg font-bold min-w-[2rem] text-center flex items-center justify-center ${
                          part === '?' 
                            ? `bg-red-900 text-red-200 ${
                                dnaState.status === 'active' 
                                  ? 'cursor-pointer hover:bg-red-700 hover:scale-105' 
                                  : 'cursor-not-allowed opacity-50'
                              }`
                            : 'bg-green-800 text-green-200'
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (part === '?' && dnaState.status === 'active') {
                            handleGapClick(index);
                          }
                        }}
                      >
                        {part}
                      </span>
                    )) : (
                      <div className="text-gray-500"></div>
                    )}
                  </div>
                </div>
              </div>
              {!serverFeedback?.show && (
                <div className="text-center text-xs text-gray-500 mt-2">
                  {dnaState.status === 'active' 
                    ? "Sélectionnez '?' pour soumettre une nucléotide."
                    : dnaState.status === 'error' 
                      ? "Erreur - Attendez le prochain round..."
                      : dnaState.status === 'success'
                        ? "Succès - Attendez le prochain round..."
                        : "Aucune session active..."}
                </div>
              )}
            </div>
          </div>
        ) : (
          // Input Screen
          <div 
            className="w-full h-full rounded p-2"
            style={{
              background: 'linear-gradient(145deg, #000000, #1a1a1a)',
              boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.5), inset 0 -2px 4px rgba(255,255,255,0.1)'
            }}
          >
            <div className="h-full flex flex-col justify-center px-2">
              {/* Current Input Display */}
              <div className="text-center mb-2">
                <div className="text-green-400 text-sm font-mono">
                  {currentInput || '_'}
                </div>
              </div>
              
              {/* ACGT Buttons */}
              <div className="flex gap-1 mb-2">
                {['A', 'C', 'G', 'T'].map(nucleotide => (
                  <button
                    key={nucleotide}
                    className={`flex-1 text-white font-bold py-1 px-1 rounded text-lg transition-all ${
                      dnaState.status === 'active' ? 'active:scale-95 cursor-pointer' : 'cursor-not-allowed opacity-50'
                    }`}
                    style={{
                      background: 'linear-gradient(145deg, #2563eb, #1d4ed8)',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)'
                    }}
                    onClick={() => dnaState.status === 'active' && handleNucleotideClick(nucleotide)}
                    disabled={dnaState.status !== 'active'}
                    onMouseDown={(e) => {
                      if (dnaState.status === 'active') {
                        e.currentTarget.style.boxShadow = 'inset 0 2px 4px rgba(0,0,0,0.4)';
                        e.currentTarget.style.transform = 'translateY(1px)';
                      }
                    }}
                    onMouseUp={(e) => {
                      if (dnaState.status === 'active') {
                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)';
                        e.currentTarget.style.transform = 'translateY(0px)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (dnaState.status === 'active') {
                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)';
                        e.currentTarget.style.transform = 'translateY(0px)';
                      }
                    }}
                  >
                    {nucleotide}
                  </button>
                ))}
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-1">
                <button
                  className={`text-white font-bold py-1 px-2 rounded text-sm transition-all ${
                    currentInput && dnaState.status === 'active' ? 'active:scale-95 cursor-pointer' : 'cursor-not-allowed'
                  }`}
                  style={{
                    background: 'linear-gradient(145deg, #16a34a, #15803d)',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)',
                    opacity: (!currentInput || dnaState.status !== 'active') ? 0.5 : 1
                  }}
                  onClick={handleSubmit}
                  disabled={!currentInput || dnaState.status !== 'active'}
                  onMouseDown={(e) => {
                    if (currentInput && dnaState.status === 'active') {
                      e.currentTarget.style.boxShadow = 'inset 0 2px 4px rgba(0,0,0,0.4)';
                      e.currentTarget.style.transform = 'translateY(1px)';
                    }
                  }}
                  onMouseUp={(e) => {
                    if (currentInput && dnaState.status === 'active') {
                      e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)';
                      e.currentTarget.style.transform = 'translateY(0px)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentInput && dnaState.status === 'active') {
                      e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)';
                      e.currentTarget.style.transform = 'translateY(0px)';
                    }
                  }}
                >
                  SUBMIT
                </button>
                
                <button
                  className="text-white font-bold py-1 px-2 rounded text-sm transition-all active:scale-95"
                  style={{
                    background: 'linear-gradient(145deg, #f59e0b, #d97706)',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)'
                  }}
                  onClick={handleBack}
                  onMouseDown={(e) => {
                    e.currentTarget.style.boxShadow = 'inset 0 2px 4px rgba(0,0,0,0.4)';
                    e.currentTarget.style.transform = 'translateY(1px)';
                  }}
                  onMouseUp={(e) => {
                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)';
                    e.currentTarget.style.transform = 'translateY(0px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3), inset 0 -1px 2px rgba(0,0,0,0.2), inset 0 1px 2px rgba(255,255,255,0.2)';
                    e.currentTarget.style.transform = 'translateY(0px)';
                  }}
                >
                  BACK
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DNAScreen;