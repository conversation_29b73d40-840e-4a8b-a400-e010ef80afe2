import { useState } from 'react';
import { showSuccessToast, showErrorToast } from '../lib/toast';
import { authClient } from '../lib/auth-client';

export default function PasswordChangeForm({ user, onPasswordChanged }) {
  // Block PERSONNE from accessing password change
  const isPersonne = user?.name?.toLowerCase() === 'personne';
  
  if (isPersonne) {
    // Redirect immediately for PERSONNE using the callback (which handles redirect URL)
    onPasswordChanged();
    return null;
  }
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!newPassword.trim() || !confirmPassword.trim()) return;

    if (newPassword !== confirmPassword) {
      showErrorToast('Passwords do not match');
      return;
    }

    if (newPassword.length < 8) {
      showErrorToast('Password must be at least 8 characters long');
      return;
    }

    setIsLoading(true);
    try {
      // Update password using better-auth
      const { error } = await authClient.changePassword({
        newPassword,
        currentPassword: user.chemicalPassword, // Use the original chemical password
      });

      if (error) {
        showErrorToast(error.message || 'Failed to update password');
        return;
      }

      // Update the mustChangePassword flag
      const updateResult = await fetch('/api/auth/update-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mustChangePassword: false,
        }),
      });

      if (updateResult.ok) {
        showSuccessToast('Password updated successfully!');
        onPasswordChanged();
      } else {
        showErrorToast('Password updated but failed to update user status');
      }
    } catch (error) {
      console.error('Error:', error);
      showErrorToast('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="text-center mb-4 sm:mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">Change Your Password</h2>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-4 relative">
          <label className="block text-gray-300 text-sm font-medium mb-2">
            New Password
          </label>
          <input
            type={showNewPassword ? "text" : "password"}
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            placeholder="Enter your new password"
            minLength={8}
            required
            disabled={isLoading}
            className="w-full p-3 rounded-lg text-base transition-all"
            style={{
              background: 'linear-gradient(145deg, #000000, #1a1a1a)',
              color: '#4ade80',
              border: '2px solid #374151',
              boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.5), inset 0 -2px 4px rgba(255,255,255,0.1)',
              paddingRight: '45px'
            }}
            onFocus={(e) => {
              e.target.style.border = '2px solid #2563eb';
            }}
            onBlur={(e) => {
              e.target.style.border = '2px solid #374151';
            }}
          />
          <button
            type="button"
            onClick={() => setShowNewPassword(!showNewPassword)}
            className="absolute right-3 top-1/2 transform text-gray-400 hover:text-gray-300 transition-colors"
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            {showNewPassword ? '🙈' : '👁️'}
          </button>
        </div>

        <div className="mb-6 relative">
          <label className="block text-gray-300 text-sm font-medium mb-2">
            Confirm Password
          </label>
          <input
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm your new password"
            minLength={8}
            required
            disabled={isLoading}
            className="w-full p-3 rounded-lg text-base transition-all"
            style={{
              background: 'linear-gradient(145deg, #000000, #1a1a1a)',
              color: '#4ade80',
              border: '2px solid #374151',
              boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.5), inset 0 -2px 4px rgba(255,255,255,0.1)',
              paddingRight: '45px'
            }}
            onFocus={(e) => {
              e.target.style.border = '2px solid #2563eb';
            }}
            onBlur={(e) => {
              e.target.style.border = '2px solid #374151';
            }}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-3 top-1/2 transform text-gray-400 hover:text-gray-300 transition-colors"
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            {showConfirmPassword ? '🙈' : '👁️'}
          </button>
        </div>

        <button
          type="submit"
          disabled={isLoading || newPassword.trim().length < 8 || newPassword !== confirmPassword}
          className="w-full p-4 rounded-lg text-base font-bold cursor-pointer transition-all select-none"
          style={{
            background: (isLoading || newPassword.trim().length < 8 || newPassword !== confirmPassword) 
              ? 'linear-gradient(145deg, #6b7280, #4b5563)'
              : 'linear-gradient(145deg, #16a34a, #15803d)',
            color: '#fff',
            border: 'none',
            boxShadow: isSubmitPressed
              ? 'inset 0 4px 8px rgba(0,0,0,0.4)'
              : '0 6px 12px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
            transform: isSubmitPressed ? 'translateY(3px)' : 'translateY(0px)',
            opacity: (isLoading || newPassword.trim().length < 8 || newPassword !== confirmPassword) ? 0.6 : 1,
            cursor: (isLoading || newPassword.trim().length < 8 || newPassword !== confirmPassword) ? 'not-allowed' : 'pointer'
          }}
          onMouseDown={() => {
            if (!isLoading && newPassword.trim().length >= 8 && newPassword === confirmPassword) {
              setIsSubmitPressed(true);
            }
          }}
          onMouseUp={() => setIsSubmitPressed(false)}
          onMouseLeave={() => setIsSubmitPressed(false)}
          onTouchStart={() => {
            if (!isLoading && newPassword.trim().length >= 8 && newPassword === confirmPassword) {
              setIsSubmitPressed(true);
            }
          }}
          onTouchEnd={() => setIsSubmitPressed(false)}
        >
          {isLoading ? 'Updating Password...' : 'Update Password'}
        </button>
      </form>
    </>
  );
}
