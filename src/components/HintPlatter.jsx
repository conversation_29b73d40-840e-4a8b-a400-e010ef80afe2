import React, { useState, useEffect } from 'react';
import { webSocketClient } from '../lib/websocket-client';

export default function HintPlatter() {
  const [currentHint, setCurrentHint] = useState(null);

  useEffect(() => {
    // Set up WebSocket callbacks
    webSocketClient.onHintCreated((hint) => {
      setCurrentHint(hint);
    });

    webSocketClient.onHintRemoved(() => {
      setCurrentHint(null);
    });

    // Connect to WebSocket
    webSocketClient.connect();

    // Cleanup on unmount
    return () => {
      webSocketClient.disconnect();
    };
  }, []);

  if (!currentHint) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      <div className="bg-yellow-400 bg-opacity-80 text-black p-4 text-center text-lg font-medium">
        {currentHint.text || "[indice]"}
      </div>
    </div>
  );
}
