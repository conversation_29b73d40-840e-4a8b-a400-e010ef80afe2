import React, { useState, useEffect } from 'react';
import { webSocketClient } from '../lib/websocket-client.ts';

const Scoreboard = () => {
  const [teamScores, setTeamScores] = useState({
    americas: { score: 0, },
    europe: { score: 0, },
    asia: { score: 0, }
  });
  const [isMobile, setIsMobile] = useState(false);

  // Check for mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Load initial scores and set up WebSocket
  useEffect(() => {
    // Fetch current scores
    const fetchCurrentScores = async () => {
      try {
        const response = await fetch('/api/scoreboard/current');
        const data = await response.json();
        setTeamScores({
          americas: { score: data.americas, },
          europe: { score: data.europe, },
          asia: { score: data.asia, }
        });
      } catch (error) {
        console.error('Error fetching current scores:', error);
      }
    };

    fetchCurrentScores();

    // Set up WebSocket connection
    webSocketClient.connect();
    
    webSocketClient.onScoreboardUpdated((scoreboard) => {
      setTeamScores({
        americas: { score: scoreboard.americas, },
        europe: { score: scoreboard.europe, },
        asia: { score: scoreboard.asia, }
      });
    });

    return () => {
      webSocketClient.disconnect();
    };
  }, []);

  const teams = [
    { 
      name: 'Amériques', 
      key: 'americas', 
      color: 'from-red-500 to-red-600',
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-500/30'
    },
    { 
      name: 'EurAfrique', 
      key: 'europe', 
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-500/30'
    },
    { 
      name: 'AustrAsie', 
      key: 'asia', 
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-500/30'
    }
  ];

  // Sort teams by score for ranking
  const sortedTeams = teams.map(team => ({
    ...team,
    score: teamScores[team.key].score,
  })).sort((a, b) => b.score - a.score);

  return (
    <div 
      className="absolute left-1/2 transform -translate-x-1/2 z-10 w-full max-w-4xl px-4"
      style={{height: isMobile ? "calc(100% - 3rem)" : "auto"}}
    >
      <div className={`bg-black/40 backdrop-blur-md rounded-lg border border-blue-500/20 p-4 ${isMobile ? 'h-full overflow-y-scroll' : ''}`}>
        <h2 className="text-xl font-bold text-white text-center mb-4">
          Classement des continents
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {sortedTeams.map((team, index) => (
            <div
              key={team.key}
              className={`relative ${team.bgColor} ${team.borderColor} border rounded-lg p-4`}
            >
              {/* Medal badge */}
              <div className={`absolute -top-2 -left-2 w-8 h-8 rounded-full flex items-center justify-center border-2 ${
                index === 0 ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 border-yellow-300' :
                index === 1 ? 'bg-gradient-to-r from-gray-300 to-gray-400 border-gray-200' :
                'bg-gradient-to-r from-amber-600 to-amber-700 border-amber-500'
              }`}>
                <span className={`text-sm font-bold ${index === 1 ? 'text-gray-700' : 'text-black'}`}>
                  #{index + 1}
                </span>
              </div>
              
              {/* Team header */}
              <div className="mb-3">
                <h3 className="text-white font-bold text-lg mb-1">{team.name}</h3>
                <div className={`h-1 bg-gradient-to-r ${team.color} rounded`}></div>
              </div>

              {/* Score */}
              <div className="text-center mb-3">
                <div className="text-3xl font-bold text-white mb-1">
                  {team.score.toLocaleString()}
                </div>
                <div className="text-sm text-gray-300">Points Totaux</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Scoreboard;
