import React, { useState, useEffect } from 'react';
import { webSocketClient } from '../lib/websocket-client';

export default function AlarmBanner() {
  const [isAlarmActive, setIsAlarmActive] = useState(false);
  const [isFlashing, setIsFlashing] = useState(false);
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [durationMinutes, setDurationMinutes] = useState(10);
  const [remainingTime, setRemainingTime] = useState(0);

  useEffect(() => {
    // Set up WebSocket callbacks for alarms
    webSocketClient.onAlarmStarted((alarm) => {
      setIsAlarmActive(true);
      if (alarm && alarm.createdAt) {
        setSessionStartTime(new Date(alarm.createdAt).getTime());
        const duration = alarm.durationMinutes || 10; // Default to 10 minutes
        setDurationMinutes(duration);
        setRemainingTime(duration * 60 * 1000); // Convert to milliseconds
      }
    });

    webSocketClient.onAlarmStopped(() => {
      setIsAlarmActive(false);
      setSessionStartTime(null);
      setDurationMinutes(10);
      setRemainingTime(0);
    });

    // Connect to WebSocket
    webSocketClient.connect();

    // Cleanup on unmount
    return () => {
      webSocketClient.disconnect();
    };
  }, []);

  useEffect(() => {
    if (isAlarmActive) {
      const flashInterval = setInterval(() => {
        setIsFlashing(prev => !prev);
      }, 500); // Flash every 500ms

      return () => clearInterval(flashInterval);
    } else {
      setIsFlashing(false);
    }
  }, [isAlarmActive]);

  // Countdown timer effect
  useEffect(() => {
    if (isAlarmActive && sessionStartTime) {
      const timerInterval = setInterval(() => {
        const elapsed = Date.now() - sessionStartTime;
        const totalDuration = durationMinutes * 60 * 1000;
        const remaining = Math.max(0, totalDuration - elapsed);
        setRemainingTime(remaining);

        // Auto-stop when time expires
        if (remaining <= 0 && isAlarmActive) {
          console.log('Session timer expired! Auto-stopping game...');
          // Call the game stop API to automatically end the session
          fetch('/api/admin/game/stop', { method: 'POST' })
            .then(response => response.json())
            .then(result => {
              if (result.success) {
                console.log('Game auto-stopped successfully');
              }
            })
            .catch(error => {
              console.error('Failed to auto-stop game:', error);
            });
        }
      }, 1000); // Update every second

      return () => clearInterval(timerInterval);
    }
  }, [isAlarmActive, sessionStartTime, durationMinutes]);

  // Format remaining time for countdown display
  const formatCountdown = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
  };

  if (!isAlarmActive) return null;

  return (
    <div 
      className={`fixed inset-0 z-50 flex items-end justify-center pb-16 pointer-events-none transition-all duration-200 ${
        isFlashing ? 'bg-red-600' : 'bg-red-700'
      }`}
      style={{ backgroundColor: isFlashing ? 'rgba(220, 38, 38, 0.4)' : 'rgba(185, 28, 28, 0.4)' }}
    >
      <div 
        className={`text-white text-6xl font-bold text-center px-8 py-4 rounded-lg shadow-2xl border-4 border-red-700 transition-all duration-200 ${
          isFlashing ? 'bg-red-600' : 'bg-red-700'
        }`}
        style={{
          textShadow: '2px 2px 4px rgba(0,0,0,0.8)',
          animation: isFlashing ? 'pulse 0.5s ease-in-out infinite' : 'none'
        }}
      >
        <div>!!! NOUVEAU VARIANT !!!</div>
        {sessionStartTime && (
          <div className={`text-2xl font-semibold mt-2 ${remainingTime <= 60000 ? 'text-yellow-300' : ''}`}>
            Time Remaining: {formatCountdown(remainingTime)}
          </div>
        )}
      </div>
      <style jsx="true">{`
        @keyframes pulse {
          from {
            transform: scale(1);
          }
          to {
            transform: scale(1.05);
          }
        }
      `}</style>
    </div>
  );
}
