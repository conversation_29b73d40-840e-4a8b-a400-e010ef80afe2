import { useState } from 'react';
import KeypadGrid from './KeypadGrid.jsx';

export default function SecurityKeypad() {
    const secretPattern = [750, 750, 350, 350];
    const tolerance = 100;
    
    const [currentPattern, setCurrentPattern] = useState([]);
    const [lastPressTime, setLastPressTime] = useState(0);
    const [display, setDisplay] = useState('');
    const [isUnlocked, setIsUnlocked] = useState(false);
    const [isLocked, setIsLocked] = useState(false);

    const checkPattern = (intervals) => {
        if (intervals.length !== 4) return false;
        
        let matches = 0;
        for (let i = 0; i < 4; i++) {
            const expected = secretPattern[i];
            const actual = intervals[i];
            
            if (Math.abs(actual - expected) <= tolerance) {
                matches++;
            }
        }
        
        return matches >= 3;
    };

    const handleKeyPress = () => {
        const currentTime = Date.now();
        
        // Prevent input when locked or unlocked
        if (isLocked || isUnlocked) {
            return;
        }
        
        // Debounce - ignore presses within 50ms of each other
        if (lastPressTime !== 0 && currentTime - lastPressTime < 50) {
            return;
        }
        
        if (lastPressTime === 0) {
            // First press
            setLastPressTime(currentTime);
            setDisplay('●');
            setCurrentPattern([]);
            return;
        }
        
        const timeDiff = currentTime - lastPressTime;
        const newPattern = [...currentPattern, timeDiff];
        
        setCurrentPattern(newPattern);
        setLastPressTime(currentTime);
        setDisplay(prev => prev + '●');
        
        // Check if we have 4 intervals (5 presses total)
        if (newPattern.length === 4) {
            if (checkPattern(newPattern)) {
                setDisplay('UNLOCKED');
                setIsUnlocked(true);
            } else {
                // Lock keypad and show incorrect password message
                setIsLocked(true);
                setDisplay('Incorrect Password');
                setTimeout(() => {
                    setCurrentPattern([]);
                    setDisplay('');
                    setLastPressTime(0);
                    setIsLocked(false);
                }, 2000);
            }
        }
    };

    return (
        <div className="security-keypad">
            <div className="keypad-container">
                <div className={`input-display ${display === 'Incorrect Password' ? 'error' : ''}`}>
                    {display}
                </div>
                
                <KeypadGrid 
                    onKeyPress={handleKeyPress} 
                    disabled={isUnlocked || isLocked}
                />
            </div>
    </div>
    );
}
