import React, { useState } from 'react';

const VaccineKey = ({ item, onPress, state = 'normal' }) => {
  const [isPressed, setIsPressed] = useState(false);

  const handleClick = () => {
    if (state !== 'error') {
      onPress(item.symbol);
    }
  };

  const handleMouseDown = () => {
    if (state !== 'error') {
      setIsPressed(true);
    }
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  const handleMouseLeave = () => {
    setIsPressed(false);
  };

  const getStateStyles = () => {
    switch (state) {
      case 'success':
        return {
          background: 'linear-gradient(145deg, #38a169, #2f855a)',
          boxShadow: '0 6px 12px rgba(56, 161, 105, 0.3), 0 2px 4px rgba(56, 161, 105, 0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
        };
      case 'error':
        return {
          background: 'linear-gradient(145deg, #e53e3e, #c53030)',
          boxShadow: '0 6px 12px rgba(229, 62, 62, 0.3), 0 2px 4px rgba(229, 62, 62, 0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
          animation: 'shake 0.5s ease-in-out forwards',
        };
      case 'pressed':
        return {
          background: 'linear-gradient(145deg, #4299e1, #3182ce)',
          boxShadow: '0 6px 12px rgba(66, 153, 225, 0.3), 0 2px 4px rgba(66, 153, 225, 0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
        };
      default:
        return isPressed
          ? {
              background: 'linear-gradient(145deg, #4a5568, #2d3748)',
              boxShadow: 'inset 0 4px 8px rgba(0,0,0,0.3), inset 0 -2px 4px rgba(255,255,255,0.1)',
              transform: 'scale(0.95) translateY(3px)',
            }
          : {
              background: 'linear-gradient(145deg, #4a5568, #2d3748)',
              boxShadow: '0 6px 12px rgba(0,0,0,0.3), 0 2px 4px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.2), inset 0 2px 4px rgba(255,255,255,0.2)',
              transform: 'scale(1) translateY(0px)',
            };
    }
  };

  return (
    <>
      <style>
        {`
          @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
          }
        `}
      </style>
      <div
        className={`w-full h-full rounded-lg flex items-center justify-center text-white text-5xl cursor-pointer transition-all duration-150 select-none ${
          isPressed && state === 'normal' ? 'scale-95' : 'scale-100'
        } ${state === 'error' ? 'cursor-not-allowed' : ''}`}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleMouseDown}
        onTouchEnd={handleMouseUp}
        style={getStateStyles()}
      >
        {item.symbol}
      </div>
    </>
  );
};

export default VaccineKey;