import React, { useState, useEffect } from 'react';

const MachineOptions = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [occupancy, setOccupancy] = useState({
    machine: null,
    manual1: null, 
    manual2: null
  });
  const [user, setUser] = useState(null);

  useEffect(() => {
    checkUserAndOccupancy();
  }, []);

  const checkUserAndOccupancy = async () => {
    try {
      // Get current user
      const userResponse = await fetch('/api/auth/get-user');
      if (!userResponse.ok) {
        throw new Error('Not authenticated');
      }
      const userData = await userResponse.json();
      setUser(userData);

      // Get current occupancy status
      const occupancyResponse = await fetch('/api/machine/occupancy');
      if (occupancyResponse.ok) {
        const occupancyData = await occupancyResponse.json();
        setOccupancy(occupancyData.occupancy); // Extract the occupancy field
      }

    } catch (err) {
      setError('Erreur de chargement des données');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const selectMachine = async (machineType) => {
    setError(null);
    
    try {
      const response = await fetch('/api/machine/request-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ machineType })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Redirect with access token
        window.location.href = `/machine${machineType === 'machine' ? '' : '/' + machineType}?token=${result.token}`;
      } else {
        setError(result.message || 'Erreur d\'accès');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
      console.error('Error requesting access:', err);
    }
  };

  if (loading) {
    return (
      <div className="h-full overflow-y-scroll bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 flex items-center justify-center">
        <div className="text-white text-xl">Chargement...</div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-scroll bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 py-4 px-4 md:py-8 md:px-8">
      <div className="max-w-4xl w-full mx-auto">
        {/* Header */}
        <div className="text-center mb-6 md:mb-12">
          <h1 className="text-2xl md:text-5xl font-bold text-white mb-1 md:mb-4">
            Sélection de Machine
          </h1>
          <p className="text-base md:text-xl text-gray-300">
            Choisissez votre poste de travail
          </p>
          {user && (
            <p className="text-xs md:text-lg text-blue-300 mt-1 md:mt-2 px-2">
              Connecté en tant que: {user.name} ({user.role === '' ? 'Personne' : user.role})
            </p>
          )}
        </div>

        {/* Error popup overlay */}
        {error && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-red-900/90 border border-red-500 rounded-lg p-6 text-center max-w-md w-full mx-4 shadow-2xl">
              <div className="text-red-100 font-semibold text-xl mb-3">⚠️ Accès Refusé</div>
              <div className="text-red-200 mb-4 text-lg">{error}</div>
              <button 
                onClick={() => setError(null)}
                className="px-6 py-3 bg-red-700 hover:bg-red-600 rounded-lg text-red-100 transition-colors font-semibold"
              >
                Fermer
              </button>
            </div>
          </div>
        )}

        {/* Machine options grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-8">
          
          {/* Machine principale */}
          <div className="relative">
            <button
              onClick={() => selectMachine('machine')}
              disabled={occupancy.machine}
              className={`w-full h-40 md:h-64 rounded-xl border-2 transition-all duration-300 ${
                occupancy.machine 
                  ? 'border-red-500 bg-red-900/30 cursor-not-allowed' 
                  : 'border-blue-500 bg-blue-900/30 hover:bg-blue-800/50 hover:border-blue-400'
              }`}
            >
              <div className="flex flex-col items-center justify-center h-full text-white p-4 md:p-6">
                <div className="text-4xl md:text-6xl mb-2 md:mb-4">🧬</div>
                <h3 className="text-lg md:text-2xl font-bold mb-1 md:mb-2">Machine Principale</h3>
                <p className="text-gray-300 text-center text-xs md:text-sm px-2">
                  Synthèse de vaccins avancée
                </p>
                
                {occupancy.machine && (
                  <div className="absolute top-2 right-2 md:top-4 md:right-4 bg-red-600 text-white px-2 py-1 md:px-3 md:py-1 rounded-full text-xs md:text-sm font-semibold">
                    OCCUPÉ
                  </div>
                )}
              </div>
            </button>
          </div>

          {/* Manuel 1 */}
          <div className="relative">
            <button
              onClick={() => selectMachine('manual1')}
              disabled={occupancy.manual1}
              className={`w-full h-40 md:h-64 rounded-xl border-2 transition-all duration-300 ${
                occupancy.manual1 
                  ? 'border-red-500 bg-red-900/30 cursor-not-allowed' 
                  : 'border-green-500 bg-green-900/30 hover:bg-green-800/50 hover:border-green-400'
              }`}
            >
              <div className="flex flex-col items-center justify-center h-full text-white p-4 md:p-6">
                <div className="text-4xl md:text-6xl mb-2 md:mb-4">📋</div>
                <h3 className="text-lg md:text-2xl font-bold mb-1 md:mb-2">Manuel 1</h3>
                <p className="text-gray-300 text-center text-xs md:text-sm px-2">
                  Procédures manuelles standard
                </p>
                
                {occupancy.manual1 && (
                  <div className="absolute top-2 right-2 md:top-4 md:right-4 bg-red-600 text-white px-2 py-1 md:px-3 md:py-1 rounded-full text-xs md:text-sm font-semibold">
                    OCCUPÉ
                  </div>
                )}
              </div>
            </button>
          </div>

          {/* Manuel 2 */}
          <div className="relative">
            <button
              onClick={() => selectMachine('manual2')}
              disabled={occupancy.manual2}
              className={`w-full h-40 md:h-64 rounded-xl border-2 transition-all duration-300 ${
                occupancy.manual2 
                  ? 'border-red-500 bg-red-900/30 cursor-not-allowed' 
                  : 'border-purple-500 bg-purple-900/30 hover:bg-purple-800/50 hover:border-purple-400'
              }`}
            >
              <div className="flex flex-col items-center justify-center h-full text-white p-4 md:p-6">
                <div className="text-4xl md:text-6xl mb-2 md:mb-4">🔬</div>
                <h3 className="text-lg md:text-2xl font-bold mb-1 md:mb-2">Manuel 2</h3>
                <p className="text-gray-300 text-center text-xs md:text-sm px-2">
                  Analyses de laboratoire
                </p>
                
                {occupancy.manual2 && (
                  <div className="absolute top-2 right-2 md:top-4 md:right-4 bg-red-600 text-white px-2 py-1 md:px-3 md:py-1 rounded-full text-xs md:text-sm font-semibold">
                    OCCUPÉ
                  </div>
                )}
              </div>
            </button>
          </div>
        </div>

        {/* Status info */}
        <div className="mt-6 md:mt-12 text-center">
          <div className="bg-black/30 rounded-lg p-3 md:p-6 max-w-2xl mx-auto">
            <h3 className="text-white text-sm md:text-lg font-semibold mb-2 md:mb-4">État des Machines</h3>
            <div className="grid grid-cols-3 gap-2 md:gap-4 text-xs md:text-sm">
              <div className={`p-2 md:p-3 rounded ${occupancy.machine ? 'bg-red-900/50 text-red-200' : 'bg-green-900/50 text-green-200'}`}>
                <div className="font-semibold">Machine</div>
                <div className="text-xs md:text-sm">{occupancy.machine ? 'Occupée' : 'Disponible'}</div>
              </div>
              <div className={`p-2 md:p-3 rounded ${occupancy.manual1 ? 'bg-red-900/50 text-red-200' : 'bg-green-900/50 text-green-200'}`}>
                <div className="font-semibold">Manuel 1</div>
                <div className="text-xs md:text-sm">{occupancy.manual1 ? 'Occupé' : 'Disponible'}</div>
              </div>
              <div className={`p-2 md:p-3 rounded ${occupancy.manual2 ? 'bg-red-900/50 text-red-200' : 'bg-green-900/50 text-green-200'}`}>
                <div className="font-semibold">Manuel 2</div>
                <div className="text-xs md:text-sm">{occupancy.manual2 ? 'Occupé' : 'Disponible'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Refresh button */}
        <div className="text-center mt-4 md:mt-8 pb-4">
          <button
            onClick={checkUserAndOccupancy}
            className="px-4 py-2 md:px-6 md:py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm md:text-base"
          >
            Actualiser
          </button>
        </div>
      </div>
    </div>
  );
};

export default MachineOptions;
