import { useState } from 'react';
import NameForm from './NameForm';
import PasswordForm from './PasswordForm';

export default function ChemicalLogin() {
  const [currentPage, setCurrentPage] = useState('name');
  const [userName, setUserName] = useState('');
  const [elements, setElements] = useState([]);
  const handleNameSubmit = (name, elementsArray) => {
    setUserName(name);
    setElements(elementsArray);
    setCurrentPage('password');
  };

  const handleBack = () => {
    setCurrentPage('name');
  };

  return (
    <div 
      className="w-full h-screen flex items-center justify-center p-5"
      style={{
        background: 'linear-gradient(145deg, #1a202c, #2d3748)'
      }}
    >
      <div 
        className="p-8 max-w-lg w-full rounded-xl"
        style={{
          background: 'linear-gradient(145deg, #4a5568, #2d3748)',
          boxShadow: '0 20px 40px rgba(0,0,0,0.3), 0 10px 20px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.3), inset 0 2px 4px rgba(255,255,255,0.1)'
        }}
      >        
        {currentPage === 'name' && (
          <NameForm onSubmit={handleNameSubmit} />
        )}
        
        {currentPage === 'password' && (
          <PasswordForm
            userName={userName}
            elements={elements}
            onBack={handleBack}
          />
        )}
      </div>
    </div>
  );
}
