import { createAuthClient } from "better-auth/react";

// Use the current origin (protocol + hostname + port) for auth client
// This ensures CORS works regardless of which hostname is used to access the app
const getBaseURL = () => {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  // Fallback for server-side rendering
  return import.meta.env.PUBLIC_APP_URL || 'http://localhost:4321';
};

export const authClient = createAuthClient({
  baseURL: getBaseURL()
});
