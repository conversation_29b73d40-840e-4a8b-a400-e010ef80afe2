// VaccineKey Game Engine
// Based on the symbols grid from symbols.md
// Players must press keys from a selected column in top-to-bottom order

export class VaccineKeyEngine {
    private activeSessions: Map<string, VaccineKeySession> = new Map();
    
    // Symbol grid from symbols.md (7 rows x 6 columns)
    private readonly SYMBOL_GRID = [
        ["⍨", "✺", "⩐", "⩩", "℘", "▥"],
        ["⏧", "▥", "⌬", "⫸", "⚚", "ᚠ"],
        ["✹", "ᚨ", "༴", "✺", "ఠ", "ఞ"],
        ["⸎", "▤", "⦞", "⫁", "⌭", "⨓"],
        ["⨋", "ᅘ", "▤", "⭈", "⍨", "⫸"],
        ["⫷", "⟪", "❋", "⍼", "༴", "☙"],
        ["☙", "໒", "ᅘ", "⧔", "⩪", "⨋"]
    ];

    generateVaccineKeySequence(): VaccineKeySequence {
        const sessionId = this.generateSessionId();
        
        // Select random column (0-5)
        const selectedColumn = Math.floor(Math.random() * 6);
        
        // Get symbols from that column (top to bottom)
        const columnSymbols = this.SYMBOL_GRID.map(row => row[selectedColumn]);
        
        // Randomly select 3-7 consecutive symbols from the column
        const minLength = 3;
        const maxLength = Math.min(7, columnSymbols.length);
        const sequenceLength = Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;
        
        // Random starting position to get consecutive symbols
        const maxStartIndex = columnSymbols.length - sequenceLength;
        const startIndex = Math.floor(Math.random() * (maxStartIndex + 1));
        
        const selectedSymbols = columnSymbols.slice(startIndex, startIndex + sequenceLength);
        
        // Create key objects with IDs
        const keys = selectedSymbols.map((symbol, index) => ({
            id: `${sessionId}_key_${index}`,
            symbol: symbol
        }));
        
        // Store the session
        const session: VaccineKeySession = {
            sessionId,
            selectedColumn,
            correctSequence: selectedSymbols,
            keys,
            currentSequence: [],
            startTime: Date.now()
        };
        
        this.activeSessions.set(sessionId, session);
        
        return {
            sequenceId: sessionId,
            keys
        };
    }

    validateKeyPress(sessionId: string, keyPress: VaccineKeyPress): VaccineKeyValidationResult {
        const session = this.activeSessions.get(sessionId);
        
        if (!session) {
            console.warn(`VaccineKey validation failed: Session ${sessionId} not found`);
            return {
                correct: false,
                points: -1,
                message: "Session not found",
                correctAnswer: []
            };
        }

        const currentIndex = session.currentSequence.length;
        const expectedSymbol = session.correctSequence[currentIndex];
        
        // Check if the current key press is correct
        if (keyPress.symbol !== expectedSymbol) {            
            // FAIL IMMEDIATELY - Clean up the session and return failure
            this.activeSessions.delete(sessionId);
            
            return {
                correct: false,
                points: -1,
                message: `Wrong key! Expected ${expectedSymbol}, got ${keyPress.symbol}`,
                correctAnswer: session.correctSequence
            };
        }

        // Correct key press - add to sequence
        session.currentSequence.push(keyPress.symbol);
        
        // Check if sequence is complete
        if (session.currentSequence.length === session.correctSequence.length) {            
            // Clean up the session
            this.activeSessions.delete(sessionId);
            
            return {
                correct: true,
                points: 3,
                message: "Sequence completed successfully!",
                correctAnswer: session.correctSequence
            };
        }
        
        return {
            correct: true,
            points: 0,
            message: "Keep going...",
            correctAnswer: [],
            inProgress: true
        };
    }

    private generateSessionId(): string {
        return `vk_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    }

    // Clean up old sessions (called periodically)
    cleanupOldSessions(maxAgeMs: number = 10 * 60 * 1000): void {
        const now = Date.now();
        for (const [sessionId, session] of this.activeSessions.entries()) {
            if (now - session.startTime > maxAgeMs) {
                this.activeSessions.delete(sessionId);
            }
        }
    }

    // Get active session count for monitoring
    getActiveSessionCount(): number {
        return this.activeSessions.size;
    }
}

export interface VaccineKeySequence {
    sequenceId: string;
    keys: VaccineKey[];
}

export interface VaccineKey {
    id: string;
    symbol: string;
}

export interface VaccineKeyPress {
    keyId: string;
    symbol: string;
    sequence?: string[]; // Current pressed sequence from client
}

export interface VaccineKeySession {
    sessionId: string;
    selectedColumn: number;
    correctSequence: string[];
    keys: VaccineKey[];
    currentSequence: string[];
    startTime: number;
}

export interface VaccineKeyValidationResult {
    correct: boolean;
    points: number;
    message: string;
    correctAnswer: string[];
    inProgress?: boolean;
}

// Singleton instance
export const vaccineKeyEngine = new VaccineKeyEngine();