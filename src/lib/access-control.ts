import { config } from "dotenv";
config();

// Hostname to location mapping
const HOSTNAME_LOCATION_MAP: Record<string, string> = {
  'washington': 'washington',
  'lima': 'lima', 
  'brasilia': 'brasilia',
  'paris': 'paris',
  'athenes': 'athenes',
  'pretoria': 'pretoria',
  'pekin': 'pekin',
  'new-delhi': 'new-delhi',
  'canberra': 'canberra',
  'nemo': 'nemo',
  'localhost': 'localhost', // For development
};

// Team access rules - which locations can access which teams
const TEAM_ACCESS_RULES: Record<string, string[]> = {
  'americas': ['washington', 'lima', 'brasilia'],
  'eurafrica': ['paris', 'athene', 'pretoria'],
  'oceasia': ['pekin', 'new-delhi', 'canberra'],
  '': ['nemo'], // Personne role (empty string)
  'admin': ['localhost'], // Admin can access from localhost only
};

export interface AccessCheckResult {
  authorized: boolean;
  reason?: string;
  location?: string;
}

/**
 * Extract hostname from request headers
 */
export function extractHostname(request: Request): string {
  const host = request.headers.get('host') || '';
  const hostname = host.split(':')[0]; // Remove port if present
  return hostname.toLowerCase();
}

/**
 * Get location from hostname
 */
export function getLocationFromHostname(hostname: string): string | null {
  return HOSTNAME_LOCATION_MAP[hostname] || null;
}

/**
 * Check if a user role can access from a specific hostname
 */
export function checkAccess(userRole: string, hostname: string): AccessCheckResult {
  const location = getLocationFromHostname(hostname);
  
  if (!location) {
    return {
      authorized: false,
      reason: 'Unknown hostname',
      location: hostname
    };
  }

  const allowedLocations = TEAM_ACCESS_RULES[userRole] || [];
  const authorized = allowedLocations.includes(location);

  return {
    authorized,
    reason: authorized ? undefined : `Team ${userRole} cannot access from ${location}`,
    location
  };
}

/**
 * Get French error message for access denial
 */
export function getAccessDeniedMessage(userRole: string, location: string): string {
  if (userRole === '') {
    return `Seul le personnel autorisé peut accéder depuis ${location}`;
  }
  
  const teamNames: Record<string, string> = {
    'americas': 'Amériques',
    'eurafrica': 'EurAfrique', 
    'oceasia': 'AustrAsie'
  };
  
  const teamName = teamNames[userRole] || userRole;
  return `L'équipe ${teamName} n'est pas autorisée à accéder depuis ${location}`;
}
