import Toastify from 'toastify-js';
import 'toastify-js/src/toastify.css';

export function showSuccessToast(message) {
  Toastify({
    text: message,
    duration: 4000,
    gravity: "top",
    position: "right",
    style: {
      background: "linear-gradient(145deg, #16a34a, #15803d)",
      borderRadius: "8px",
      boxShadow: "0 4px 8px rgba(0,0,0,0.3)"
    },
    stopOnFocus: true
  }).showToast();
}

export function showErrorToast(message) {
  Toastify({
    text: message,
    duration: 4000,
    gravity: "top",
    position: "right",
    style: {
      background: "linear-gradient(145deg, #dc2626, #b91c1c)",
      borderRadius: "8px",
      boxShadow: "0 4px 8px rgba(0,0,0,0.3)"
    },
    stopOnFocus: true
  }).showToast();
}

export function showInfoToast(message) {
  Toastify({
    text: message,
    duration: 3000,
    gravity: "top",
    position: "right",
    style: {
      background: "linear-gradient(145deg, #2563eb, #1d4ed8)",
      borderRadius: "8px",
      boxShadow: "0 4px 8px rgba(0,0,0,0.3)"
    },
    stopOnFocus: true
  }).showToast();
}
