import { config } from "dotenv";
config();

export type MachineType = 'machine' | 'manual1' | 'manual2';

export interface MachineOccupancy {
  userId: string;
  sessionId: string;
  timestamp: number;
  userRole: string;
}

export interface SessionMachines {
  machine: MachineOccupancy | null;
  manual1: MachineOccupancy | null;
  manual2: MachineOccupancy | null;
}

export interface AccessToken {
  userId: string;
  sessionId: string;
  machineType: MachineType;
  timestamp: number;
  expires: number;
}

/**
 * Manages machine occupancy per session and access tokens
 */
class MachineSessionManager {
  private sessionMachines: Map<string, SessionMachines> = new Map();
  private accessTokens: Map<string, AccessToken> = new Map();
  private readonly TOKEN_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

  /**
   * Get current occupancy for a session
   */
  getSessionOccupancy(sessionId: string): SessionMachines {
    if (!this.sessionMachines.has(sessionId)) {
      this.sessionMachines.set(sessionId, {
        machine: null,
        manual1: null,
        manual2: null
      });
    }
    return this.sessionMachines.get(sessionId)!;
  }

  /**
   * Check if a machine is occupied in a session
   */
  isMachineOccupied(sessionId: string, machineType: MachineType): boolean {
    const occupancy = this.getSessionOccupancy(sessionId);
    return occupancy[machineType] !== null;
  }

  /**
   * Get the user occupying a specific machine
   */
  getMachineOccupant(sessionId: string, machineType: MachineType): MachineOccupancy | null {
    const occupancy = this.getSessionOccupancy(sessionId);
    return occupancy[machineType];
  }

  /**
   * Check if a user is already occupying any machine
   */
  isUserOccupyingAnyMachine(sessionId: string, userId: string): { occupied: boolean, machineType?: MachineType } {
    const occupancy = this.getSessionOccupancy(sessionId);
    
    for (const machineType of ['machine', 'manual1', 'manual2'] as MachineType[]) {
      const occupant = occupancy[machineType];
      if (occupant && occupant.userId === userId) {
        return { occupied: true, machineType };
      }
    }
    
    return { occupied: false };
  }

  /**
   * Occupy a machine in a session
   */
  occupyMachine(sessionId: string, machineType: MachineType, userId: string, userRole: string): boolean {
    const occupancy = this.getSessionOccupancy(sessionId);
    const currentOccupant = occupancy[machineType];
    
    // Check if user is already occupying ANY machine
    const userOccupancy = this.isUserOccupyingAnyMachine(sessionId, userId);
    if (userOccupancy.occupied && userOccupancy.machineType !== machineType) {
      return false;
    }
    
    // If already occupied by someone else, return false
    if (currentOccupant && currentOccupant.userId !== userId) {
      return false;
    }

    // Set or update occupancy
    occupancy[machineType] = {
      userId,
      sessionId,
      timestamp: Date.now(),
      userRole
    };

    return true;
  }

  /**
   * Release a machine in a session
   */
  releaseMachine(sessionId: string, machineType: MachineType, userId: string): boolean {
    const occupancy = this.getSessionOccupancy(sessionId);
    const currentOccupant = occupancy[machineType];

    if (!currentOccupant || currentOccupant.userId !== userId) {
      return false; // Not occupied by this user
    }

    occupancy[machineType] = null;
    return true;
  }

  /**
   * Release all machines for a specific user
   */
  releaseAllMachinesForUser(userId: string): void {
    for (const [sessionId, machines] of this.sessionMachines) {
      for (const machineType of ['machine', 'manual1', 'manual2'] as MachineType[]) {
        const occupant = machines[machineType];
        if (occupant && occupant.userId === userId) {
          machines[machineType] = null;
        }
      }
    }
  }

  /**
   * Generate an access token for a machine
   */
  generateAccessToken(userId: string, sessionId: string, machineType: MachineType): string {
    const token = `${userId}-${sessionId}-${machineType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.accessTokens.set(token, {
      userId,
      sessionId,
      machineType,
      timestamp: Date.now(),
      expires: Date.now() + this.TOKEN_EXPIRY_MS
    });

    // Clean expired tokens
    this.cleanExpiredTokens();
    
    return token;
  }

  /**
   * Validate an access token
   */
  validateAccessToken(token: string): AccessToken | null {
    const tokenData = this.accessTokens.get(token);
    
    if (!tokenData) {
      return null;
    }

    if (Date.now() > tokenData.expires) {
      this.accessTokens.delete(token);
      return null;
    }

    return tokenData;
  }

  /**
   * Consume an access token (use it once)
   */
  consumeAccessToken(token: string): AccessToken | null {
    const tokenData = this.validateAccessToken(token);
    if (tokenData) {
      this.accessTokens.delete(token);
    }
    return tokenData;
  }

  /**
   * Clean expired tokens
   */
  private cleanExpiredTokens(): void {
    const now = Date.now();
    for (const [token, tokenData] of this.accessTokens) {
      if (now > tokenData.expires) {
        this.accessTokens.delete(token);
      }
    }
  }

  /**
   * Get all occupancy status for debugging
   */
  getAllOccupancy(): Record<string, SessionMachines> {
    const result: Record<string, SessionMachines> = {};
    for (const [sessionId, machines] of this.sessionMachines) {
      result[sessionId] = { ...machines };
    }
    return result;
  }

  /**
   * Clean up session data when session ends
   */
  cleanupSession(sessionId: string): void {
    this.sessionMachines.delete(sessionId);
    
    // Remove tokens for this session
    for (const [token, tokenData] of this.accessTokens) {
      if (tokenData.sessionId === sessionId) {
        this.accessTokens.delete(token);
      }
    }
  }
}

// Singleton instance
export const machineSessionManager = new MachineSessionManager();
