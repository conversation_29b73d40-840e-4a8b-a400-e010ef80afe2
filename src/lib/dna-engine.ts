export interface DNASequence {
  sessionId: string;
  sequence: string[];
  missingIndex: number;
  correctAnswer: string;
}

export interface DNASession {
  sessionId: string;
  sequence: string[];
  missingIndex: number;
  correctAnswer: string;
  startTime: number;
}

export interface DNAValidationResult {
  correct: boolean;
  points: number;
  message: string;
  correctAnswer?: string;
}

export class DNAGameEngine {
  private nucleotides = ['A', 'G', 'T', 'C'];
  private activeSessions: Map<string, DNASession> = new Map();

  generateDnaSequence(): DNASequence {
    const sessionId = this.generateSessionId();
    
    const length = Math.floor(Math.random() * 4) + 4; // 4-7 nucleotides total (including ?)
    const sequence = Array(length).fill(null).map(() => 
      this.nucleotides[Math.floor(Math.random() * this.nucleotides.length)]
    );

    const missingIndex = Math.floor(Math.random() * length);
    sequence[missingIndex] = '?';

    const correctAnswer = this.calculateCorrectAnswer(sequence);    

    // Store the session
    const session: DNASession = {
      sessionId,
      sequence,
      missingIndex,
      correctAnswer,
      startTime: Date.now()
    };
    
    this.activeSessions.set(sessionId, session);

    return {
      sessionId,
      sequence,
      missingIndex,
      correctAnswer
    };
  }

  validateDnaAnswer(sessionId: string, answer: string): DNAValidationResult {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      console.warn(`DNA validation failed: Session ${sessionId} not found`);
      return {
        correct: false,
        points: -1,
        message: "Session not found"
      };
    }

    if (!this.nucleotides.includes(answer)) {
      // Clean up the session on failure
      this.activeSessions.delete(sessionId);
      
      return {
        correct: false,
        points: -1,
        message: `Invalid nucleotide: ${answer}`,
        correctAnswer: session.correctAnswer
      };
    }
    
    const isCorrect = answer === session.correctAnswer;
    
    if (!isCorrect) {
      // Clean up the session on failure
      this.activeSessions.delete(sessionId);
      
      return {
        correct: false,
        points: -1,
        message: `Wrong nucleotide! Expected ${session.correctAnswer}`,
        correctAnswer: session.correctAnswer
      };
    }
    
    // Clean up the session on success
    this.activeSessions.delete(sessionId);
    
    return {
      correct: true,
      points: 3,
      message: "DNA sequence completed successfully!"
    };
  }

  // Legacy method for backward compatibility
  generateSequence(): DNASequence {
    return this.generateDnaSequence();
  }

  // Legacy method for backward compatibility  
  validateAnswer(sequence: string[], answer: string): boolean {
    if (!this.nucleotides.includes(answer)) return false;
    
    const correctAnswer = this.calculateCorrectAnswer(sequence);
    return answer === correctAnswer;
  }

  private generateSessionId(): string {
    return `dna_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  // Clean up old sessions (called periodically)
  cleanupOldSessions(maxAgeMs: number = 10 * 60 * 1000): void {
    const now = Date.now();
    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now - session.startTime > maxAgeMs) {
        this.activeSessions.delete(sessionId);
      }
    }
  }

  // Get active session count for monitoring
  getActiveSessionCount(): number {
    return this.activeSessions.size;
  }

  private calculateCorrectAnswer(sequence: string[]): string {
    const length = sequence.length;
    
    if (length === 4) {
      return this.calculate3PlusQuestionMark(sequence);
    } else if (length === 5) {
      return this.calculate4PlusQuestionMark(sequence);
    } else if (length === 6) {
      return this.calculate5PlusQuestionMark(sequence);
    } else if (length === 7) {
      return this.calculate6PlusQuestionMark(sequence);
    }
    
    return sequence[sequence.length - 1];
  }

  private calculate3PlusQuestionMark(sequence: string[]): string {
    // Real nucleotides for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const lastActualNucleotide = realNucleotides[realNucleotides.length - 1];
    
    if (aCount === 0) {
      return realNucleotides[1]; // second nucleotide excluding '?'
    } else if (lastActualNucleotide === 'C') {
      return lastActualNucleotide; // last nucleotide excluding '?'
    } else if (gCount > 1) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else {
      return lastActualNucleotide; // last nucleotide excluding '?'
    }
  }

  private calculate4PlusQuestionMark(sequence: string[]): string {
    // Real nucleotides for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const gCount = realNucleotides.filter(n => n === 'G').length;
    const lastRealNucleotide = realNucleotides[realNucleotides.length - 1];
    
    if (aCount > 1) {
      return lastRealNucleotide; // last nucleotide excluding '?'
    } else if (lastRealNucleotide === 'T' && aCount === 0) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else if (gCount === 1) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else if (tCount > 1) {
      return lastRealNucleotide; // last nucleotide excluding '?'
    } else {
      return realNucleotides[1]; // second nucleotide excluding '?'
    }
  }

  private calculate5PlusQuestionMark(sequence: string[]): string {
    // Real nucleotides for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const lastRealNucleotide = realNucleotides[realNucleotides.length - 1];

    if (lastRealNucleotide === 'C') {
      return realNucleotides[3]; // fourth nucleotide excluding '?'
    } else if (aCount === 1 && tCount > 1) {
      return realNucleotides[0]; // first nucleotide excluding '?'
    } else if (cCount === 0) {
      return realNucleotides[1]; // second nucleotide excluding '?'
    } else {
      return realNucleotides[0]; // first nucleotide excluding '?'
    }
  }

  private calculate6PlusQuestionMark(sequence: string[]): string {
    // Filter out the '?' for counting and position references
    const realNucleotides = sequence.filter(n => n !== '?');
    const aCount = realNucleotides.filter(n => n === 'A').length;
    const tCount = realNucleotides.filter(n => n === 'T').length;
    const cCount = realNucleotides.filter(n => n === 'C').length;
    const lastRealNucleotide = realNucleotides[realNucleotides.length - 1];
    
    if (tCount === 0) {
      return realNucleotides[2]; // third nucleotide excluding '?'
    } else if (tCount === 1 && cCount > 1) {
      return realNucleotides[3]; // fourth nucleotide excluding '?'
    } else if (aCount === 0) {
      return lastRealNucleotide; // last nucleotide excluding '?'
    } else {
      return realNucleotides[3]; // fourth nucleotide excluding '?'
    }
  }
}

export const dnaGameEngine = new DNAGameEngine();
