export const elements: Record<string, { name: string; number: number }> = {
    H: { name: "Hydrogen", number: 1 },
    He: { name: "Helium", number: 2 },
    Li: { name: "Lithium", number: 3 },
    Be: { name: "Beryllium", number: 4 },
    B: { name: "<PERSON>ron", number: 5 },
    C: { name: "Carbon", number: 6 },
    N: { name: "Nitrogen", number: 7 },
    O: { name: "Oxygen", number: 8 },
    F: { name: "Fluorine", number: 9 },
    Ne: { name: "Neon", number: 10 },
    Na: { name: "Sodium", number: 11 },
    Mg: { name: "Magnesium", number: 12 },
    Al: { name: "Aluminum", number: 13 },
    Si: { name: "Silicon", number: 14 },
    P: { name: "Phosphorus", number: 15 },
    S: { name: "Sulfur", number: 16 },
    Cl: { name: "Chlorine", number: 17 },
    Ar: { name: "Argon", number: 18 },
    K: { name: "Potassium", number: 19 },
    Ca: { name: "Calcium", number: 20 },
    Sc: { name: "Scandium", number: 21 },
    Ti: { name: "Titanium", number: 22 },
    V: { name: "Vanadium", number: 23 },
    Cr: { name: "Chromium", number: 24 },
    Mn: { name: "Manganese", number: 25 },
    Fe: { name: "Iron", number: 26 },
    Co: { name: "Cobalt", number: 27 },
    Ni: { name: "Nickel", number: 28 },
    Cu: { name: "Copper", number: 29 },
    Zn: { name: "Zinc", number: 30 },
    Ga: { name: "Gallium", number: 31 },
    Ge: { name: "Germanium", number: 32 },
    As: { name: "Arsenic", number: 33 },
    Se: { name: "Selenium", number: 34 },
    Br: { name: "Bromine", number: 35 },
    Kr: { name: "Krypton", number: 36 },
    Rb: { name: "Rubidium", number: 37 },
    Sr: { name: "Strontium", number: 38 },
    Y: { name: "Yttrium", number: 39 },
    Zr: { name: "Zirconium", number: 40 },
    Nb: { name: "Niobium", number: 41 },
    Mo: { name: "Molybdenum", number: 42 },
    Tc: { name: "Technetium", number: 43 },
    Ru: { name: "Ruthenium", number: 44 },
    Rh: { name: "Rhodium", number: 45 },
    Pd: { name: "Palladium", number: 46 },
    Ag: { name: "Silver", number: 47 },
    Cd: { name: "Cadmium", number: 48 },
    In: { name: "Indium", number: 49 },
    Sn: { name: "Tin", number: 50 },
    Sb: { name: "Antimony", number: 51 },
    Te: { name: "Tellurium", number: 52 },
    I: { name: "Iodine", number: 53 },
    Xe: { name: "Xenon", number: 54 },
    Cs: { name: "Cesium", number: 55 },
    Ba: { name: "Barium", number: 56 },
    La: { name: "Lanthanum", number: 57 },
    Ce: { name: "Cerium", number: 58 },
    Pr: { name: "Praseodymium", number: 59 },
    Nd: { name: "Neodymium", number: 60 },
    Pm: { name: "Promethium", number: 61 },
    Sm: { name: "Samarium", number: 62 },
    Eu: { name: "Europium", number: 63 },
    Gd: { name: "Gadolinium", number: 64 },
    Tb: { name: "Terbium", number: 65 },
    Dy: { name: "Dysprosium", number: 66 },
    Ho: { name: "Holmium", number: 67 },
    Er: { name: "Erbium", number: 68 },
    Tm: { name: "Thulium", number: 69 },
    Yb: { name: "Ytterbium", number: 70 },
    Lu: { name: "Lutetium", number: 71 },
    Hf: { name: "Hafnium", number: 72 },
    Ta: { name: "Tantalum", number: 73 },
    W: { name: "Tungsten", number: 74 },
    Re: { name: "Rhenium", number: 75 },
    Os: { name: "Osmium", number: 76 },
    Ir: { name: "Iridium", number: 77 },
    Pt: { name: "Platinum", number: 78 },
    Au: { name: "Gold", number: 79 },
    Hg: { name: "Mercury", number: 80 },
    Tl: { name: "Thallium", number: 81 },
    Pb: { name: "Lead", number: 82 },
    Bi: { name: "Bismuth", number: 83 },
    Po: { name: "Polonium", number: 84 },
    At: { name: "Astatine", number: 85 },
    Rn: { name: "Radon", number: 86 },
    Fr: { name: "Francium", number: 87 },
    Ra: { name: "Radium", number: 88 },
    Ac: { name: "Actinium", number: 89 },
    Th: { name: "Thorium", number: 90 },
    Pa: { name: "Protactinium", number: 91 },
    U: { name: "Uranium", number: 92 },
    Np: { name: "Neptunium", number: 93 },
    Pu: { name: "Plutonium", number: 94 },
    Am: { name: "Americium", number: 95 },
    Cm: { name: "Curium", number: 96 },
    Bk: { name: "Berkelium", number: 97 },
    Cf: { name: "Californium", number: 98 },
    Es: { name: "Einsteinium", number: 99 },
    Fm: { name: "Fermium", number: 100 },
    Md: { name: "Mendelevium", number: 101 },
    No: { name: "Nobelium", number: 102 },
    Lr: { name: "Lawrencium", number: 103 },
    Rf: { name: "Rutherfordium", number: 104 },
    Db: { name: "Dubnium", number: 105 },
    Sg: { name: "Seaborgium", number: 106 },
    Bh: { name: "Bohrium", number: 107 },
    Hs: { name: "Hassium", number: 108 },
    Mt: { name: "Meitnerium", number: 109 },
    Ds: { name: "Darmstadtium", number: 110 },
    Rg: { name: "Roentgenium", number: 111 },
    Cn: { name: "Copernicium", number: 112 },
    Nh: { name: "Nihonium", number: 113 },
    Fl: { name: "Flerovium", number: 114 },
    Mc: { name: "Moscovium", number: 115 },
    Lv: { name: "Livermorium", number: 116 },
    Ts: { name: "Tennessine", number: 117 },
    Og: { name: "Oganesson", number: 118 }
};

export function nameToElements(name: string): string[] | null {
  name = name.toUpperCase().replace(/[^A-Z]/g, '');
  
  // Try to find a valid combination using backtracking
  function findCombination(startIndex: number, currentResult: string[]): string[] | null {
    if (startIndex === name.length) {
      return currentResult;
    }
    
    // Try 2-character element first, then 1-character
    for (let len = Math.min(2, name.length - startIndex); len >= 1; len--) {
      const rawSymbol = name.substring(startIndex, startIndex + len);
      // Proper chemical element case: first letter uppercase, rest lowercase
      const symbol = rawSymbol.charAt(0).toUpperCase() + rawSymbol.slice(1).toLowerCase();
      if (elements[symbol]) {
        const result = findCombination(startIndex + len, [...currentResult, symbol]);
        if (result) {
          return result;
        }
      }
    }
    
    return null;
  }
  
  return findCombination(0, []);
}

export function generatePassword(elementSymbols: string[]): string {
  return elementSymbols.map(symbol => elements[symbol].number).join('');
}