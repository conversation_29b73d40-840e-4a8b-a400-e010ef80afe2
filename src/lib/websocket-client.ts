export interface WebSocketMessage {
  type: 'hint_created' | 'hint_dismissed' | 'alarm_started' | 'alarm_stopped' | 'scoreboard_updated' | 'session_created' | 'session_closed' | 'round_started' | 'round_ended' | 'answer_validation';
  hint?: {
    id: string;
    text: string;
    createdAt: string;
  };
  alarm?: {
    id: string;
    createdAt: string;
  };
  scoreboard?: {
    americas: number;
    europe: number;
    asia: number;
    updatedAt: string;
  };
  sessionData?: {
    team: string;
    sessionId?: string;
  };
  roundData?: {
    sessionId: string;
    roundId: string;
    gameState: {
      vaccineKeys: {
        keys: any[];
        pressedSequence: string[];
        keyStates: { [keyId: string]: 'normal' | 'success' | 'error' };
        status: 'idle' | 'active' | 'success' | 'error';
      };
      dnaScreen: {
        sequence: string[];
        status: 'idle' | 'active' | 'success' | 'error';
        feedback: {
          show: boolean;
          correct: boolean;
          points: number;
          message?: string;
        } | null;
      };
    };
    roundComplete: boolean;
  };
  validationResult?: {
    sessionId: string;
    roundId: string;
    gameType: 'dna' | 'vaccine_key';
    succeeded: boolean;
    totalPoints: number;
    correctAnswer?: string;
    triggeredByModule: string;
    feedback?: {
      show: boolean;
      correct: boolean;
      points: number;
      message?: string;
    };
    keyStates?: { [keyId: string]: 'normal' | 'success' | 'error' };
  };
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private onHintUpdate: ((hint: any) => void) | null = null;
  private onHintDismiss: (() => void) | null = null;
  private onAlarmStart: ((alarm?: any) => void) | null = null;
  private onAlarmStop: (() => void) | null = null;
  private onScoreboardUpdate: ((scoreboard: any) => void) | null = null;
  private onSessionCreatedCallback: ((sessionData: any) => void) | null = null;
  private onSessionClosedCallback: ((sessionData: any) => void) | null = null;
  private onRoundStartedCallback: ((roundData: any) => void) | null = null;
  private onRoundEndedCallback: ((roundData: any) => void) | null = null;
  private onAnswerValidationCallback: ((result: any) => void) | null = null;

  async ensureConnection() {
    // If WebSocket is open, we're good
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    // If we're in a failed state, reset
    if (this.ws && (this.ws.readyState === WebSocket.CLOSED || this.ws.readyState === WebSocket.CLOSING)) {
      this.ws = null;
      this.isConnecting = false;
    }

    // If we're already connecting and it's not failed, wait
    if (this.isConnecting && this.ws && this.ws.readyState === WebSocket.CONNECTING) {
      return;
    }

    // Initialize WebSocket server before connecting
    try {
      await fetch('/api/websocket/init');
    } catch (error) {
      console.log('WebSocket server initialization failed, attempting connection anyway:', error);
    }

    this.connect();
    
    // Wait for connection to establish with timeout
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('WebSocket connection timeout'));
      }, 10000); // 10 second timeout
      
      const checkConnection = () => {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          clearTimeout(timeout);
          resolve(void 0);
        } else if (this.ws && this.ws.readyState === WebSocket.CLOSED) {
          clearTimeout(timeout);
          reject(new Error('WebSocket connection failed'));
        } else {
          setTimeout(checkConnection, 100);
        }
      };
      
      checkConnection();
    });
  }

  connect() {
    if (typeof window === 'undefined') {
      return;
    }
    
    if (this.isConnecting) {
      return;
    }
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    this.isConnecting = true;
    const wsUrl = import.meta.env.PUBLIC_WS_URL;

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        this.reconnectAttempts = 0;
        this.isConnecting = false;
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          
          if (message.type === 'hint_created' && message.hint) {
            this.onHintUpdate?.(message.hint);
          } else if (message.type === 'hint_dismissed') {
            this.onHintDismiss?.();
          } else if (message.type === 'alarm_started') {
            this.onAlarmStart?.(message.alarm);
          } else if (message.type === 'alarm_stopped') {
            this.onAlarmStop?.();
          } else if (message.type === 'scoreboard_updated' && message.scoreboard) {
            this.onScoreboardUpdate?.(message.scoreboard);
          } else if (message.type === 'session_created' && message.sessionData) {
            this.onSessionCreatedCallback?.(message.sessionData);
          } else if (message.type === 'session_closed' && message.sessionData) {
            this.onSessionClosedCallback?.(message.sessionData);
          } else if (message.type === 'round_started' && message.roundData) {
            this.onRoundStartedCallback?.(message.roundData);
          } else if (message.type === 'round_ended' && message.roundData) {
            this.onRoundEndedCallback?.(message.roundData);
          } else if (message.type === 'answer_validation' && message.validationResult) {
            this.onAnswerValidationCallback?.(message.validationResult);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        this.isConnecting = false;
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
        this.attemptReconnect();
      };
    } catch (error) {
      this.isConnecting = false;
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  onHintCreated(callback: (hint: any) => void) {
    this.onHintUpdate = callback;
  }

  onHintRemoved(callback: () => void) {
    this.onHintDismiss = callback;
  }

  onAlarmStarted(callback: (alarm?: any) => void) {
    this.onAlarmStart = callback;
  }

  onAlarmStopped(callback: () => void) {
    this.onAlarmStop = callback;
  }

  onScoreboardUpdated(callback: (scoreboard: any) => void) {
    this.onScoreboardUpdate = callback;
  }


  onSessionCreated(callback: (sessionData: any) => void) {
    this.onSessionCreatedCallback = callback;
  }

  onSessionClosed(callback: (sessionData: any) => void) {
    this.onSessionClosedCallback = callback;
  }

  onRoundStarted(callback: (roundData: any) => void) {
    this.onRoundStartedCallback = callback;
  }

  onRoundEnded(callback: (roundData: any) => void) {
    this.onRoundEndedCallback = callback;
  }

  onAnswerValidation(callback: (result: any) => void) {
    this.onAnswerValidationCallback = callback;
  }

  submitDnaAnswer(team: string, sessionId: string, answer: string) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify({
          type: 'submit_dna_answer',
          team,
          sessionId,
          answer
        }));
      } catch (error) {
        console.error('Failed to send DNA answer:', error);
      }
    } else {
      console.error('WebSocket not connected, cannot send DNA answer');
    }
  }

  submitVaccineKeyAnswer(team: string, sessionId: string, answer: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify({
          type: 'submit_vaccine_key_answer',
          team,
          sessionId,
          answer
        }));
      } catch (error) {
        console.error('Failed to send vaccine key answer:', error);
      }
    } else {
      console.error('WebSocket not connected, cannot send vaccine key answer');
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Singleton instance
export const webSocketClient = new WebSocketClient();
