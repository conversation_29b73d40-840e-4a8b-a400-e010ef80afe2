import { config } from "dotenv";
config(); // Load environment variables

import { betterAuth } from "better-auth";
import { Pool } from "pg";

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const auth = betterAuth({
  database: pool,
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    minPasswordLength: 4,
  },
  user: {
    additionalFields: {
      elements: {
        type: "string",
        required: false,
      },
      chemicalPassword: {
        type: "string",
        required: false,
      },
      mustChangePassword: {
        type: "boolean",
        required: false,
        defaultValue: true,
      },
      role: {
        type: "string",
        required: true,
      },
    },
  },
  trustedOrigins: (() => {
    const allowedHosts = process.env.PUBLIC_ALLOWED_HOST?.split(',').map(host => host.trim()) || ['localhost'];
    const origins = [
      ...allowedHosts.flatMap(host => [
        `https://${host}`,
        `http://${host}`,
        `https://${host}:4321`,
        `http://${host}:4321`,
        `https://${host}:3000`,
        `http://${host}:3000`,
      ]),
      "http://localhost:3000", // Keep for development WebSocket server
    ];
    return [...new Set(origins)]; // Remove duplicates
  })(),
  secret: process.env.BETTER_AUTH_SECRET!,
});
