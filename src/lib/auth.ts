import { config } from "dotenv";
config(); // Load environment variables

import { betterAuth } from "better-auth";
import { Pool } from "pg";

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const auth = betterAuth({
  database: pool,
  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false,
    minPasswordLength: 4,
  },
  user: {
    additionalFields: {
      elements: {
        type: "string",
        required: false,
      },
      chemicalPassword: {
        type: "string",
        required: false,
      },
      mustChangePassword: {
        type: "boolean",
        required: false,
        defaultValue: true,
      },
      role: {
        type: "string",
        required: true,
      },
    },
  },
  trustedOrigins: ["http://localhost:4321", "http://localhost:3000"],
  secret: process.env.BETTER_AUTH_SECRET!,
});
