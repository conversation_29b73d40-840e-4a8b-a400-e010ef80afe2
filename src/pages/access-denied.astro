---
import Layout from '../layouts/Layout.astro';

// Get the error message from query parameters
const url = new URL(Astro.request.url);
const message = url.searchParams.get('message') || 'Accès non autorisé';
---

<Layout title="Accès Refusé">
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-800 flex items-center justify-center py-4 px-4">
    <div class="max-w-md w-full mx-auto text-center">
      <div class="bg-black/30 backdrop-blur-sm rounded-lg p-6 md:p-8 border border-red-500/20">
        <div class="text-6xl mb-6">🚫</div>
        <h1 class="text-2xl md:text-3xl font-bold text-white mb-4">
          Accès Refusé
        </h1>
        <p class="text-red-300 text-base md:text-lg mb-6">
          {message}
        </p>
        <div class="space-y-3">
          <a 
            href="/dashboard" 
            class="block w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
          >
            Retour au Tableau de Bord
          </a>
          <a 
            href="/logout" 
            class="block w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
          >
            Se Déconnecter
          </a>
        </div>
      </div>
    </div>
  </div>
</Layout>
