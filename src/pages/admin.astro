---
import Layout from '../layouts/Layout.astro';
import { auth } from '../lib/auth';

const session = await auth.api.getSession({
  headers: Astro.request.headers,
});

if (!session?.user || session.user.role !== 'admin') {
  return Astro.redirect('/login');
}
---

<Layout title="Admin Panel">
  <div class="h-full overflow-y-scroll bg-gray-900 text-white p-8">
    <h1 class="text-3xl font-bold mb-8">Admin Panel</h1>
    
    <div class="max-w-2xl">
      <div class="bg-gray-800 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">Hints</h2>
        <form id="hintForm" class="space-y-4">
          <div>
            <textarea
              id="hintText"
              placeholder="Entrez votre indice ici..."
              class="w-full p-3 bg-gray-700 text-white rounded border border-gray-600 focus:border-yellow-400 focus:outline-none"
              rows="4"
              required
            ></textarea>
          </div>
          <div class="flex flex-col space-y-4">
          <button
            type="submit"
            class="bg-yellow-400 text-black px-6 py-2 rounded font-semibold hover:bg-yellow-300 transition-colors"
          >
            Publier l'indice
          </button>
          <button
            id="dismissBtn"
            class="bg-red-500 text-white px-6 py-2 rounded font-semibold hover:bg-red-400 transition-colors"
          >
            Supprimer l'indice actuel
          </button>
          </div>
        </form>
      </div>

      <div class="bg-gray-800 rounded-lg p-6">
        <h2 class="text-2xl font-semibold mb-6 text-center">🎮 Game Control</h2>
        
        <!-- Game Status Display -->
        <div class="bg-gray-700 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-center space-x-4">
            <div class="text-center">
              <div class="text-sm text-gray-400">Game Status</div>
              <div id="gameStatus" class="text-lg font-bold">Loading...</div>
            </div>
            <div class="text-center">
              <div class="text-sm text-gray-400">Alarm</div>
              <div id="alarmStatus" class="text-lg font-bold">Loading...</div>
            </div>
            <div class="text-center">
              <div class="text-sm text-gray-400">Active Sessions</div>
              <div id="sessionCount" class="text-lg font-bold">Loading...</div>
            </div>
          </div>
        </div>

        <!-- Timer Duration Setting -->
        <div class="bg-gray-700 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-center space-x-4">
            <label for="timerDuration" class="text-white font-medium">Session Duration:</label>
            <input
              id="timerDuration"
              type="number"
              min="1"
              max="120"
              value="10"
              class="bg-gray-600 text-white px-3 py-2 rounded border border-gray-500 focus:border-blue-400 focus:outline-none w-20 text-center"
            />
            <span class="text-gray-400">minutes</span>
          </div>
        </div>

        <!-- Unified Game Controls -->
        <div class="flex space-x-4 justify-center mb-6">
          <button
            id="startGameBtn"
            class="bg-green-600 hover:bg-green-500 text-white px-8 py-4 rounded-lg text-xl font-bold transition-all transform hover:scale-105 shadow-lg"
          >
            🚀 START GAME
          </button>
          <button
            id="stopGameBtn"
            class="bg-red-600 hover:bg-red-500 text-white px-8 py-4 rounded-lg text-xl font-bold transition-all transform hover:scale-105 shadow-lg"
          >
            🛑 STOP GAME
          </button>
        </div>

        <!-- Team Status Monitoring -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium text-yellow-400 text-center">Team Status</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="bg-gray-700 rounded-lg p-3 text-center">
              <h4 class="text-sm font-medium text-blue-300 mb-1">Americas</h4>
              <div id="americasStatus" class="font-mono text-sm">Inactive</div>
            </div>
            <div class="bg-gray-700 rounded-lg p-3 text-center">
              <h4 class="text-sm font-medium text-green-300 mb-1">Eurafrica</h4>
              <div id="eurafricaStatus" class="font-mono text-sm">Inactive</div>
            </div>
            <div class="bg-gray-700 rounded-lg p-3 text-center">
              <h4 class="text-sm font-medium text-orange-300 mb-1">Oceasia</h4>
              <div id="oceasiaStatus" class="font-mono text-sm">Inactive</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    import { showSuccessToast, showErrorToast } from '../lib/toast.js';

    // Hint management elements
    const form = document.getElementById('hintForm');
    const dismissBtn = document.getElementById('dismissBtn');
    
    // Unified game control elements
    const startGameBtn = document.getElementById('startGameBtn');
    const stopGameBtn = document.getElementById('stopGameBtn');
    
    // Status display elements
    const gameStatus = document.getElementById('gameStatus');
    const alarmStatus = document.getElementById('alarmStatus');
    const sessionCount = document.getElementById('sessionCount');
    const americasStatus = document.getElementById('americasStatus');
    const eurafricaStatus = document.getElementById('eurafricaStatus');
    const oceasiaStatus = document.getElementById('oceasiaStatus');

    // Hint management (unchanged)
    form?.addEventListener('submit', async (e) => {
      e.preventDefault();
      const text = (document.getElementById('hintText') as HTMLTextAreaElement)?.value;
      
      if (!text.trim()) return;

      try {
        const response = await fetch('/api/admin/hints-create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ text: text.trim() })
        });

        if (response.ok) {
          (document.getElementById('hintText') as HTMLTextAreaElement).value = '';
          showSuccessToast('Indice publié avec succès!');
        } else {
          showErrorToast('Erreur lors de la publication');
        }
      } catch (error) {
        console.error('Error:', error);
        showErrorToast('Erreur lors de la publication');
      }
    });

    dismissBtn?.addEventListener('click', async () => {
      try {
        const response = await fetch('/api/admin/hints-dismiss', {
          method: 'POST'
        });

        if (response.ok) {
          showSuccessToast('Indice supprimé avec succès!');
        } else {
          showErrorToast('Erreur lors de la suppression');
        }
      } catch (error) {
        console.error('Error:', error);
        showErrorToast('Erreur lors de la suppression');
      }
    });

    // Unified game control functions
    async function startGame() {
      if (!confirm('Êtes-vous sûr de vouloir démarrer le jeu ? Cela va déclencher l\'alarme et créer toutes les sessions d\'équipe.')) {
        return;
      }

      const timerDuration = parseInt(document.getElementById('timerDuration').value) || 10;

      startGameBtn.disabled = true;
      startGameBtn.textContent = '🚀 STARTING...';

      try {
        const response = await fetch('/api/admin/game/start', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ durationMinutes: timerDuration })
        });

        const result = await response.json();
        
        if (response.ok && result.success) {
          showSuccessToast(result.message);
          loadGameStatus(); // Refresh status
        } else {
          showErrorToast(result.message || 'Erreur lors du démarrage du jeu');
        }
      } catch (error) {
        console.error('Error:', error);
        showErrorToast('Erreur réseau lors du démarrage du jeu');
      } finally {
        startGameBtn.disabled = false;
        startGameBtn.textContent = '🚀 START GAME';
      }
    }

    async function stopGame() {
      if (!confirm('Êtes-vous sûr de vouloir arrêter le jeu ? Cela va arrêter l\'alarme et toutes les sessions d\'équipe.')) {
        return;
      }

      stopGameBtn.disabled = true;
      stopGameBtn.textContent = '🛑 STOPPING...';

      try {
        const response = await fetch('/api/admin/game/stop', {
          method: 'POST'
        });

        const result = await response.json();
        
        if (response.ok && result.success) {
          showSuccessToast(result.message);
          loadGameStatus(); // Refresh status
        } else {
          showErrorToast(result.message || 'Erreur lors de l\'arrêt du jeu');
        }
      } catch (error) {
        console.error('Error:', error);
        showErrorToast('Erreur réseau lors de l\'arrêt du jeu');
      } finally {
        stopGameBtn.disabled = false;
        stopGameBtn.textContent = '🛑 STOP GAME';
      }
    }

    // Status update functions
    function updateGameStatus(statusData) {
      // Update overall game status
      const { gameState, alarm, sessions } = statusData;
      
      gameStatus.textContent = gameState.toUpperCase();
      gameStatus.className = `text-lg font-bold ${
        gameState === 'active' ? 'text-green-400' : 
        gameState === 'partial' ? 'text-yellow-400' : 
        'text-gray-400'
      }`;
      
      // Update alarm status
      alarmStatus.textContent = alarm.active ? 'ACTIVE' : 'INACTIVE';
      alarmStatus.className = `text-lg font-bold ${alarm.active ? 'text-red-400' : 'text-gray-400'}`;
      
      // Update session count
      sessionCount.textContent = `${sessions.active}/${sessions.total}`;
      sessionCount.className = `text-lg font-bold ${
        sessions.active === sessions.total ? 'text-green-400' : 
        sessions.active > 0 ? 'text-yellow-400' : 
        'text-gray-400'
      }`;
      
      // Update individual team statuses
      const teamElements = { Americas: americasStatus, Eurafrica: eurafricaStatus, Oceasia: oceasiaStatus };
      
      sessions.teams.forEach(team => {
        const element = teamElements[team.team];
        if (element) {
          const status = team.active ? 'Active' : 'Inactive';
          element.textContent = status;
          element.className = `font-mono text-sm ${team.active ? 'text-green-400' : 'text-gray-400'}`;
        }
      });
    }

    // Load game status
    async function loadGameStatus() {
      try {
        const response = await fetch('/api/admin/game/status');
        if (response.ok) {
          const statusData = await response.json();
          if (statusData.success) {
            updateGameStatus(statusData);
          }
        }
      } catch (error) {
        console.error('Error loading game status:', error);
      }
    }

    // Event listeners
    startGameBtn?.addEventListener('click', startGame);
    stopGameBtn?.addEventListener('click', stopGame);

    // Initialize status on page load
    loadGameStatus();
    
    // Auto-refresh status every 5 seconds
    setInterval(loadGameStatus, 5000);
  </script>
</Layout>
