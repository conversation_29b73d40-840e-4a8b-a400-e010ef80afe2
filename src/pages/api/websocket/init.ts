import type { APIRoute } from 'astro';
import { ensureGameWebSocketInitialized } from '../../../lib/websocket-server';

export const GET: APIRoute = async () => {
  try {
    // Initialize WebSocket server
    ensureGameWebSocketInitialized();
    
    return new Response(JSON.stringify({ 
      success: true,
      message: 'WebSocket server initialized' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error initializing WebSocket server:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to initialize WebSocket server' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};