import type { APIRoute } from 'astro';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const GET: APIRoute = async () => {
  try {
    // Check if there are any records
    const countResult = await pool.query('SELECT COUNT(*) FROM scoreboard');
    if (parseInt(countResult.rows[0].count) === 0) {
      // Insert initial scores
      await pool.query(
        `INSERT INTO scoreboard (americas, europe, asia, "updatedAt") VALUES (0, 0, 0, NOW())`
      );
    }

    // Get the latest scoreboard
    const result = await pool.query(
      'SELECT * FROM scoreboard ORDER BY "updatedAt" DESC LIMIT 1'
    );

    if (result.rows.length === 0) {
      return new Response(JSON.stringify({ americas: 0, europe: 0, asia: 0, updatedAt: new Date().toISOString() }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const scoreboard = result.rows[0];
    return new Response(JSON.stringify({
      americas: scoreboard.americas,
      europe: scoreboard.europe,
      asia: scoreboard.asia,
      updatedAt: scoreboard.updatedAt.toISOString()
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error getting current scoreboard:', error);
    return new Response(JSON.stringify({ error: 'Failed to get scoreboard' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
