import type { APIRoute } from 'astro';
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const GET: APIRoute = async () => {
  try {
    const result = await pool.query(
      'SELECT * FROM alarms WHERE "isActive" = true ORDER BY "createdAt" DESC LIMIT 1'
    );

    return new Response(JSON.stringify(result.rows[0] || null), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error getting active alarm:', error);
    return new Response('Internal server error', { status: 500 });
  }
};
