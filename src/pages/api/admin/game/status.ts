import { config } from "dotenv";
config(); // Load environment variables

import type { APIRoute } from 'astro';
import { Pool } from 'pg';
import { gameWsManager, ensureGameWebSocketInitialized } from '../../../../lib/websocket-server';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const GET: APIRoute = async () => {
  try {
    // Ensure WebSocket server is initialized
    ensureGameWebSocketInitialized();
    
    console.log('📊 Getting unified game status...');
    
    // Step 1: Get alarm status from database
    const alarmResult = await pool.query(
      'SELECT * FROM alarms WHERE "isActive" = true ORDER BY "createdAt" DESC LIMIT 1'
    );
    
    const alarmActive = alarmResult.rows.length > 0;
    
    // Step 2: Get all team session statuses
    const teams = [
      { display: 'Americas', backend: 'americas' },
      { display: 'Eurafrica', backend: 'eurafrica' },
      { display: 'Oceasia', backend: 'oceasia' }
    ];
    const teamStatuses = [];
    
    // Get session status from the websocket manager
    const sessionStatus = gameWsManager.getSessionStatus();
    
    for (const team of teams) {
      try {
        const isActive = sessionStatus[team.backend] || false;
        
        teamStatuses.push({
          team: team.display,
          active: isActive,
          sessionData: isActive ? {
            backend: team.backend,
            status: 'active'
          } : null
        });
      } catch (error) {
        console.error(`❌ Error getting status for team ${team.display}:`, error);
        teamStatuses.push({
          team: team.display,
          active: false,
          sessionData: null,
          error: error.message
        });
      }
    }
    
    // Determine overall game state
    const activeSessions = teamStatuses.filter(t => t.active).length;
    const totalSessions = teams.length;
    
    let gameState = 'idle';
    if (alarmActive && activeSessions > 0) {
      gameState = 'active';
    } else if (alarmActive || activeSessions > 0) {
      gameState = 'partial'; // Some components active, some not
    }
    
    console.log(`📊 Game status: ${gameState}, Alarm: ${alarmActive}, Sessions: ${activeSessions}/${totalSessions}`);
    
    return new Response(JSON.stringify({
      success: true,
      gameState,
      alarm: {
        active: alarmActive
      },
      sessions: {
        active: activeSessions,
        total: totalSessions,
        teams: teamStatuses
      },
      summary: {
        fullyActive: gameState === 'active',
        fullyInactive: gameState === 'idle',
        partiallyActive: gameState === 'partial'
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('💥 Failed to get game status:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to get game status: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}