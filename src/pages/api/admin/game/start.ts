import type { APIRoute } from 'astro';
import { Pool } from 'pg';
import { gameWsManager, ensureGameWebSocketInitialized } from '../../../../lib/websocket-server';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const POST: APIRoute = async ({ locals, request }) => {
  try {
    // Ensure WebSocket server is initialized
    ensureGameWebSocketInitialized();
    
    // Parse request body for duration
    let durationMinutes = 10; // Default 10 minutes
    try {
      const body = await request.json();
      if (body.durationMinutes && typeof body.durationMinutes === 'number') {
        durationMinutes = Math.max(1, Math.min(120, body.durationMinutes)); // Clamp between 1-120 minutes
      }
    } catch {
      // Use default if JSON parsing fails
    }
    
    console.log(`🎮 Starting unified game (alarm + all sessions) for ${durationMinutes} minutes...`);
    
    // Step 1: Start the alarm in database
    // First deactivate any existing active alarms
    await pool.query('UPDATE alarms SET "isActive" = false WHERE "isActive" = true');
    
    // Create new active alarm with duration
    const alarmResult = await pool.query(
      'INSERT INTO alarms (id, "isActive", "createdBy", "durationMinutes") VALUES ($1, $2, $3, $4) RETURNING *',
      [crypto.randomUUID(), true, locals.user!.id, durationMinutes]
    );
    
    const newAlarm = alarmResult.rows[0];
    console.log('⏰ Alarm started in database');
    
    // Broadcast the alarm via WebSocket
    gameWsManager.broadcastAlarmStarted(newAlarm);
    
    // Step 2: Create sessions for all teams
    const teams = [
      { display: 'Americas', backend: 'americas' },
      { display: 'Eurafrica', backend: 'eurafrica' },
      { display: 'Oceasia', backend: 'oceasia' }
    ];
    const sessionResults = [];
    
    for (const team of teams) {
      try {
        const sessionId = gameWsManager.createTeamSession(team.backend);
        sessionResults.push({ team: team.display, success: true, sessionId });
        console.log(`✅ Session created for team ${team.display} (${team.backend})`);
      } catch (error) {
        console.error(`❌ Failed to create session for team ${team.display}:`, error);
        sessionResults.push({ team: team.display, success: false, error: error.message });
      }
    }
    
    // Check if any sessions failed
    const failedSessions = sessionResults.filter(r => !r.success);
    
    if (failedSessions.length > 0) {
      console.warn('⚠️ Some sessions failed to start:', failedSessions);
      // Continue anyway - partial success is better than rollback
    }
    
    const successfulSessions = sessionResults.filter(r => r.success);
    
    return new Response(JSON.stringify({
      success: true,
      message: `Game started! Alarm active, ${successfulSessions.length}/${teams.length} team sessions created.`,
      alarm: { active: true },
      sessions: sessionResults
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('💥 Failed to start game:', error);
    
    // Try to rollback alarm if it was started
    try {
      await pool.query('UPDATE alarms SET "isActive" = false WHERE "isActive" = true');
      console.log('🔄 Rolled back alarm activation');
    } catch (rollbackError) {
      console.error('💥 Failed to rollback alarm:', rollbackError);
    }
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to start game: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}