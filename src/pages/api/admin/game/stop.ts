import type { APIRoute } from 'astro';
import { Pool } from 'pg';
import { gameWsManager, ensureGameWebSocketInitialized } from '../../../../lib/websocket-server';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const POST: APIRoute = async () => {
  try {
    // Ensure WebSocket server is initialized
    ensureGameWebSocketInitialized();
    
    console.log('🛑 Stopping unified game (alarm + all sessions)...');
    
    // Step 1: Stop the alarm in database
    await pool.query('UPDATE alarms SET "isActive" = false WHERE "isActive" = true');
    console.log('⏰ Alarm stopped in database');
    
    // Broadcast alarm stop via WebSocket
    gameWsManager.broadcastAlarmStopped();
    
    // Step 2: Stop sessions for all teams
    const teams = [
      { display: 'Americas', backend: 'americas' },
      { display: 'Eurafrica', backend: 'eurafrica' },
      { display: 'Oceasia', backend: 'oceasia' }
    ];
    const sessionResults = [];
    
    for (const team of teams) {
      try {
        gameWsManager.stopTeamSession(team.backend);
        sessionResults.push({ team: team.display, success: true });
        console.log(`✅ Session stopped for team ${team.display} (${team.backend})`);
      } catch (error) {
        console.error(`❌ Failed to stop session for team ${team.display}:`, error);
        sessionResults.push({ team: team.display, success: false, error: error.message });
      }
    }
    
    // Check if any sessions failed to stop
    const failedSessions = sessionResults.filter(r => !r.success);
    
    if (failedSessions.length > 0) {
      console.warn('⚠️ Some sessions failed to stop:', failedSessions);
      // Continue anyway - partial success is acceptable for stop operations
    }
    
    const successfulSessions = sessionResults.filter(r => r.success);
    
    return new Response(JSON.stringify({
      success: true,
      message: `Game stopped! Alarm inactive, ${successfulSessions.length}/${teams.length} team sessions stopped.`,
      alarm: { active: false },
      sessions: sessionResults
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('💥 Failed to stop game:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to stop game: ' + error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}