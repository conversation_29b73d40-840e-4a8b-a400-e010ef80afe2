import type { APIRoute } from 'astro';
import { Pool } from 'pg';
import { gameWsManager, ensureGameWebSocketInitialized } from '../../../lib/websocket-server';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const POST: APIRoute = async ({ request }) => {
  try {
    // Ensure WebSocket server is initialized
    ensureGameWebSocketInitialized();
    
    const { americas, europe, asia } = await request.json();

    // Validate input
    if (typeof americas !== 'number' || typeof europe !== 'number' || typeof asia !== 'number') {
      return new Response(JSON.stringify({ error: 'Invalid score data' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Insert new scores
    const result = await pool.query(
      `INSERT INTO scoreboard (americas, europe, asia, "updatedAt") 
       VALUES ($1, $2, $3, NOW()) 
       RETURNING *`,
      [americas, europe, asia]
    );

    const updatedScoreboard = result.rows[0];

    // Broadcast to WebSocket clients
    gameWsManager.broadcastScoreboardUpdate({
      americas: updatedScoreboard.americas,
      europe: updatedScoreboard.europe,
      asia: updatedScoreboard.asia,
      updatedAt: updatedScoreboard.updatedAt.toISOString()
    });

    return new Response(JSON.stringify(updatedScoreboard), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error updating scoreboard:', error);
    return new Response(JSON.stringify({ error: 'Failed to update scoreboard' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
