import type { APIRoute } from 'astro';
import { Pool } from 'pg';
import { gameWsManager, ensureGameWebSocketInitialized } from '../../../lib/websocket-server';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // Ensure WebSocket server is initialized
    ensureGameWebSocketInitialized();
    
    const { text } = await request.json();
    
    if (!text) {
      return new Response('Text is required', { status: 400 });
    }

    // Deactivate any existing active hints
    await pool.query('UPDATE hints SET "isActive" = false WHERE "isActive" = true');

    // Create new hint
    const result = await pool.query(
      'INSERT INTO hints (id, text, "createdBy") VALUES ($1, $2, $3) RETURNING *',
      [crypto.randomUUID(), text, locals.user!.id]
    );

    const newHint = result.rows[0];
    
    // Broadcast the new hint via WebSocket
    gameWsManager.broadcastHintCreated(newHint);

    return new Response(JSON.stringify(newHint), {
      status: 201,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Error creating hint:', error);
    return new Response('Internal server error', { status: 500 });
  }
};
