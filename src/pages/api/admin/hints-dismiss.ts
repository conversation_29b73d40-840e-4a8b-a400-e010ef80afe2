import type { APIRoute } from 'astro';
import { Pool } from 'pg';
import { gameWsManager, ensureGameWebSocketInitialized } from '../../../lib/websocket-server';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const POST: APIRoute = async () => {
  try {
    // Ensure WebSocket server is initialized
    ensureGameWebSocketInitialized();
    
    // Deactivate all active hints
    await pool.query('UPDATE hints SET "isActive" = false WHERE "isActive" = true');

    // Broadcast hint dismissal via WebSocket
    gameWsManager.broadcastHintDismissed();

    return new Response('OK', { status: 200 });
  } catch (error) {
    console.error('Error dismissing hints:', error);
    return new Response('Internal server error', { status: 500 });
  }
};
