import type { APIRoute } from 'astro';
import { nameToElements, generatePassword } from '../../../lib/chemical';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { name, password } = body;

    if (!name || !password) {
      return new Response(JSON.stringify({ error: "Name and password are required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const elementSymbols = nameToElements(name.trim());
    if (!elementSymbols) {
      return new Response(JSON.stringify({ error: "Invalid name" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const correctPassword = generatePassword(elementSymbols);
    
    if (password.trim() !== correctPassword) {
      return new Response(JSON.stringify({ error: "Invalid password" }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Create user with chemical element data
    const userData = {
      id: crypto.randomUUID(),
      name: name.trim(),
      email: `${name.trim().toLowerCase()}@vertven.com`,
      emailVerified: true,
      elements: elementSymbols,
      chemicalPassword: correctPassword,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Create session
    const sessionData = {
      id: crypto.randomUUID(),
      userId: userData.id,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      token: crypto.randomUUID()
    };

    return new Response(JSON.stringify({
      user: userData,
      session: sessionData,
      elements: elementSymbols,
      message: `Welcome, ${name.trim()}! Access Granted.`
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ success: false, error: 'Server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
