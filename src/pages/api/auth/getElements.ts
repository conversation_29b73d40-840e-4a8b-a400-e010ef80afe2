import type { APIRoute } from 'astro';
import { nameToElements, generatePassword } from '../../../lib/chemical';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const body = await request.json();
    const { name } = body;

    if (!name) {
      return new Response(JSON.stringify({ error: "Name is required" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Special case for admin users
    if (name.trim().toLowerCase() === 'admin') {
      return new Response(JSON.stringify({
        success: true,
        elements: ['ADMIN'],
        password: 'admin'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const elementSymbols = nameToElements(name.trim());
    if (!elementSymbols || elementSymbols.length === 0) {
      return new Response(JSON.stringify({ error: "Invalid name" }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const correctPassword = generatePassword(elementSymbols);
    
    return new Response(JSON.stringify({
      success: true,
      elements: elementSymbols,
      password: correctPassword
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ success: false, error: 'Server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
