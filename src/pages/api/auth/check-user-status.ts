import type { APIRoute } from 'astro';
import { config } from "dotenv";
import { Pool } from "pg";

config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
});

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    const { userName } = await request.json();
    
    if (!userName) {
      return new Response(JSON.stringify({ error: 'Username is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const email = `${userName.toLowerCase()}@vertven.com`;
    
    // Check if user exists and get their mustChangePassword status
    const user = await pool.query(
      'SELECT "mustChangePassword" FROM "user" WHERE email = $1',
      [email]
    );

    if (user.rows.length === 0) {
      return new Response(JSON.stringify({ 
        userExists: false,
        isFirstLogin: true 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const mustChangePassword = user.rows[0].mustChangePassword;
    
    return new Response(JSON.stringify({ 
      userExists: true,
      isFirstLogin: mustChangePassword === true || mustChangePassword === null
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('Check user status error:', error);
    return new Response(JSON.stringify({ error: 'Server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
