import { config } from "dotenv";
config();

import type { APIRoute } from 'astro';
import { machineSessionManager } from '../../../lib/machine-session-manager';
import { teamSessionManager } from '../../../lib/team-session-manager';
import { auth } from '../../../lib/auth';

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const session = locals.session;

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Non authentifié'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user data with role from auth session
    const authSession = await auth.api.getSession({
      headers: request.headers,
    });

    if (!authSession?.user) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Session utilisateur introuvable'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const user = authSession.user;
    const body = await request.json();
    const { machineType } = body;

    if (!machineType || !['machine', 'manual1', 'manual2'].includes(machineType)) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Type de machine invalide'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Use a single global session for machine access control
    // This ensures 1 player per machine globally, not per team
    const currentSessionId = 'global_machine_session';
    
    if (!currentSessionId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Aucune session active trouvée'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Release the machine
    const released = machineSessionManager.releaseMachine(
      currentSessionId, 
      machineType, 
      user.id
    );

    if (!released) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Impossible de libérer cette machine'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      message: 'Machine libérée avec succès'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error releasing machine:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Erreur serveur'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
