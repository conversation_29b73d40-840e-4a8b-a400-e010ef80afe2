import { config } from "dotenv";
config();

import type { APIRoute } from 'astro';
import { machineSessionManager } from '../../../lib/machine-session-manager';
import { teamSessionManager } from '../../../lib/team-session-manager';
import { checkAccess, extractHostname, getAccessDeniedMessage } from '../../../lib/access-control';
import { auth } from '../../../lib/auth';

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    const session = locals.session;

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Non authentifié'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user data with role from auth session
    const authSession = await auth.api.getSession({
      headers: request.headers,
    });

    if (!authSession?.user) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Session utilisateur introuvable'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const user = authSession.user;

    const body = await request.json();
    const { machineType } = body;

    if (!machineType || !['machine', 'manual1', 'manual2'].includes(machineType)) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Type de machine invalide'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check hostname-based access control
    const hostname = extractHostname(request);
    const accessCheck = checkAccess(user.role, hostname);
    
    if (!accessCheck.authorized) {
      return new Response(JSON.stringify({
        success: false,
        message: getAccessDeniedMessage(user.role, accessCheck.location || hostname)
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Use a single global session for machine access control
    // This ensures 1 player per machine globally, not per team
    const currentSessionId = 'global_machine_session';
    
    if (!currentSessionId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Aucune session active trouvée'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check current occupancy
    const occupant = machineSessionManager.getMachineOccupant(currentSessionId, machineType);
    const userOccupancy = machineSessionManager.isUserOccupyingAnyMachine(currentSessionId, user.id);

    // Check if user is already on another machine
    if (userOccupancy.occupied && userOccupancy.machineType !== machineType) {
      return new Response(JSON.stringify({
        success: false,
        message: `Vous occupez déjà la machine ${userOccupancy.machineType}. Quittez-la avant d'accéder à une autre machine.`
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if machine is already occupied by someone else
    if (occupant && occupant.userId !== user.id) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Cette machine est déjà occupée par un autre utilisateur'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Try to occupy the machine (handles re-occupying by same user)
    const occupied = machineSessionManager.occupyMachine(
      currentSessionId, 
      machineType, 
      user.id, 
      user.role
    );

    if (!occupied) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Impossible d\'occuper cette machine'
      }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate access token
    const token = machineSessionManager.generateAccessToken(
      user.id, 
      currentSessionId, 
      machineType
    );

    return new Response(JSON.stringify({
      success: true,
      token,
      machineType,
      sessionId: currentSessionId,
      message: 'Accès autorisé'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error requesting machine access:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Erreur serveur'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
