import { config } from "dotenv";
config();

import type { APIRoute } from 'astro';
import { machineSessionManager } from '../../../lib/machine-session-manager';
import { teamSessionManager } from '../../../lib/team-session-manager';
import { auth } from '../../../lib/auth';

export const GET: APIRoute = async ({ request, locals }) => {
  try {
    const session = locals.session;

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Non authentifié'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user data with role from auth session
    const authSession = await auth.api.getSession({
      headers: request.headers,
    });

    if (!authSession?.user) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Session utilisateur introuvable'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const user = authSession.user;
    
    // Use a single global session for machine access control
    // This ensures 1 player per machine globally, not per team
    const currentSessionId = 'global_machine_session';
    
    if (!currentSessionId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Aucune session active'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get occupancy for current session
    const occupancy = machineSessionManager.getSessionOccupancy(currentSessionId);
    
    // Format response to show only if machines are occupied, not by whom
    const response = {
      success: true,
      occupancy: {
        machine: occupancy.machine !== null,
        manual1: occupancy.manual1 !== null,
        manual2: occupancy.manual2 !== null
      },
      sessionId: currentSessionId
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error getting machine occupancy:', error);
    
    return new Response(JSON.stringify({
      success: false,
      message: 'Erreur serveur'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
