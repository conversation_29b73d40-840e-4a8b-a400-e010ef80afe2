---
import Layout from '../../layouts/Layout.astro';
import { checkAccess, extractHostname, getAccessDeniedMessage } from '../../lib/access-control';
import { auth } from '../../lib/auth';

// Check authentication
const session = Astro.locals.session;
if (!session) {
  const redirectUrl = encodeURIComponent('/machine/manual2');
  return Astro.redirect(`/login?redirect=${redirectUrl}`);
}

// Get user data with role for hostname access control
const authSession = await auth.api.getSession({
  headers: Astro.request.headers,
});

if (!authSession?.user) {
  return Astro.redirect(`/login?redirect=${encodeURIComponent('/machine/manual2')}`);
}

// Check hostname-based access control
const hostname = extractHostname(Astro.request);
const accessCheck = checkAccess(authSession.user.role, hostname);

if (!accessCheck.authorized) {
  // Create an error page or redirect with error message
  const errorMessage = getAccessDeniedMessage(authSession.user.role, accessCheck.location || hostname);
  const errorUrl = `/access-denied?message=${encodeURIComponent(errorMessage)}`;
  return Astro.redirect(errorUrl);
}

// Check for access token
const url = new URL(Astro.request.url);
const token = url.searchParams.get('token');

if (!token) {
  // No token, redirect to options page
  return Astro.redirect('/machine/options');
}

// Validate token on server side
try {
  const response = await fetch(`${url.origin}/api/machine/validate-token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...Object.fromEntries(Astro.request.headers)
    },
    body: JSON.stringify({ token, machineType: 'manual2' })
  });

  if (!response.ok) {
    // Invalid token, redirect to options
    return Astro.redirect('/machine/options');
  }
} catch (error) {
  console.error('Token validation error:', error);
  return Astro.redirect('/machine/options');
}
---

<Layout title="Manuel 2 - Analyses de Laboratoire">
  <div class="h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 py-4 px-4 relative">
    {/* Back to Options Button */}
    <div class="absolute top-4 right-4 z-50">
      <a 
        href="/machine/options" 
        class="flex items-center gap-2 px-3 py-2 md:px-4 md:py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm shadow-lg"
      >
        <span>←</span>
        <span class="hidden sm:inline">Options</span>
      </a>
    </div>

    <div class="max-w-4xl w-full h-full overflow-y-scroll mx-auto text-center">
      <h1 class="text-2xl md:text-4xl font-bold text-white mb-2 md:mb-4">
        🔬 Manuel 2
      </h1>
      <h2 class="text-lg md:text-xl text-purple-300 mb-4 md:mb-6 px-2">
        Analyses de Laboratoire Avancées
      </h2>
      
      <div class="bg-black/30 backdrop-blur-sm rounded-lg p-3 md:p-6 border border-purple-500/20 mb-4">
        <div class="text-white text-base md:text-lg mb-4 md:mb-6">
          Manuel ADN - Règles de Réplication des Nucléotides
        </div>
        
        <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg mb-3 text-left">
          <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2 md:mb-3">🔬 Règle Générale</h3>
          <div class="text-gray-300 text-xs md:text-sm">
            <p><strong>Sauf mention contraire explicite, le '?' est exclu dans la séquence de nucléotides pour déterminer la position à répliquer.</strong></p>
          </div>
        </div>

        <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg mb-3 text-left">
          <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2 md:mb-3">💡 Exemple</h3>
          <div class="text-gray-300 text-xs md:text-sm space-y-1">
            <p><strong>Avec C ? T C</strong>, le premier nucléotide est C, le second est T, le troisième est C.</p>
            <p>Au total il y a 4 nucléotides incluant le '?'.</p>
            <p>Il n'y a pas de A, donc on réplique le second nucléotide, qui est <strong>T</strong>.</p>
          </div>
        </div>

        <div class="space-y-3">
          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 4 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• S'il n'y a pas de A, répliquer le <strong>second nucléotide</strong>.</p>
              <p>• Sinon, si le dernier nucléotide est C, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, s'il y a plus d'un G, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>dernier nucléotide</strong>.</p>
            </div>
          </div>

          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 5 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• S'il y a plus d'un A, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, si le dernier nucléotide est T et qu'il n'y a pas de A, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, s'il y a exactement un G, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, s'il y a plus d'un T, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>second nucléotide</strong>.</p>
            </div>
          </div>

          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 6 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• Si le dernier nucléotide est C, répliquer le <strong>quatrième nucléotide</strong>.</p>
              <p>• Sinon, s'il y a exactement un A et qu'il y a plus d'un T, répliquer le <strong>premier nucléotide</strong>.</p>
              <p>• Sinon, s'il n'y a pas de C, répliquer le <strong>second nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>premier nucléotide</strong>.</p>
            </div>
          </div>

          <div class="bg-purple-900/30 p-3 md:p-4 rounded-lg text-left">
            <h3 class="text-purple-300 font-bold text-sm md:text-lg mb-2">🧬 7 nucléotides incluant le '?'</h3>
            <div class="text-gray-300 text-xs md:text-sm space-y-1">
              <p>• S'il n'y a pas de T, répliquer le <strong>troisième nucléotide</strong>.</p>
              <p>• Sinon, s'il y a exactement un T et qu'il y a plus d'un C, répliquer le <strong>quatrième nucléotide</strong>.</p>
              <p>• Sinon, s'il n'y a pas de A, répliquer le <strong>dernier nucléotide</strong>.</p>
              <p>• Sinon, répliquer le <strong>quatrième nucléotide</strong>.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // Release machine when user leaves the page
    function releaseMachine() {
      navigator.sendBeacon('/api/machine/release', JSON.stringify({
        machineType: 'manual2'
      }));
    }

    // Handle page unload
    window.addEventListener('beforeunload', releaseMachine);
    window.addEventListener('pagehide', releaseMachine);
    
    // Handle navigation away (for SPAs)
    window.addEventListener('popstate', releaseMachine);
  </script>
</Layout>
