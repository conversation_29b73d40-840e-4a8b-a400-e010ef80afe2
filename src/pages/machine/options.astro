---
import Layout from '../../layouts/Layout.astro';
import MachineOptions from '../../components/MachineOptions.jsx';
import { checkAccess, extractHostname, getAccessDeniedMessage } from '../../lib/access-control';
import { auth } from '../../lib/auth';

// This page handles access control and machine selection
const user = Astro.locals.user;
const session = Astro.locals.session;

// Require authentication
if (!session || !user) {
  const redirectUrl = encodeURIComponent('/machine/options');
  return Astro.redirect(`/login?redirect=${redirectUrl}`);
}

// Get user data with role for hostname access control
const authSession = await auth.api.getSession({
  headers: Astro.request.headers,
});

if (!authSession?.user) {
  return Astro.redirect(`/login?redirect=${encodeURIComponent('/machine/options')}`);
}

// Check hostname-based access control
const hostname = extractHostname(Astro.request);
const accessCheck = checkAccess(authSession.user.role, hostname);

if (!accessCheck.authorized) {
  // Create an error page or redirect with error message
  const errorMessage = getAccessDeniedMessage(authSession.user.role, accessCheck.location || hostname);
  const errorUrl = `/access-denied?message=${encodeURIComponent(errorMessage)}`;
  return Astro.redirect(errorUrl);
}
---

<Layout title="Sélection de Machine" showHeader={true}>
  <MachineOptions client:load />
</Layout>
