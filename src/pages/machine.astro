---
import Layout from '../layouts/Layout.astro';
import VaccineMachine from '../components/VaccineMachine.jsx';
import { checkAccess, extractHostname, getAccessDeniedMessage } from '../lib/access-control';
import { auth } from '../lib/auth';

// Check authentication
const session = Astro.locals.session;
if (!session) {
  const redirectUrl = encodeURIComponent('/machine');
  return Astro.redirect(`/login?redirect=${redirectUrl}`);
}

// Get user data with role for hostname access control
const authSession = await auth.api.getSession({
  headers: Astro.request.headers,
});

if (!authSession?.user) {
  return Astro.redirect(`/login?redirect=${encodeURIComponent('/machine')}`);
}

// Check hostname-based access control
const hostname = extractHostname(Astro.request);
const accessCheck = checkAccess(authSession.user.role, hostname);

if (!accessCheck.authorized) {
  // Create an error page or redirect with error message
  const errorMessage = getAccessDeniedMessage(authSession.user.role, accessCheck.location || hostname);
  const errorUrl = `/access-denied?message=${encodeURIComponent(errorMessage)}`;
  return Astro.redirect(errorUrl);
}

// Check for access token
const url = new URL(Astro.request.url);
const token = url.searchParams.get('token');

if (!token) {
  // No token, redirect to options page
  return Astro.redirect('/machine/options');
}

// Validate token on server side
try {
  const response = await fetch(`${url.origin}/api/machine/validate-token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...Object.fromEntries(Astro.request.headers)
    },
    body: JSON.stringify({ token, machineType: 'machine' })
  });

  if (!response.ok) {
    // Invalid token, redirect to options
    return Astro.redirect('/machine/options');
  }
} catch (error) {
  console.error('Token validation error:', error);
  return Astro.redirect('/machine/options');
}
---

<Layout title="Machine de Synthèse de Vaccins">
	<VaccineMachine client:load />
	
	<script>
		// Release machine when user leaves the page
		function releaseMachine() {
			navigator.sendBeacon('/api/machine/release', JSON.stringify({
				machineType: 'machine'
			}));
		}

		// Handle page unload
		window.addEventListener('beforeunload', releaseMachine);
		window.addEventListener('pagehide', releaseMachine);
		
		// Handle navigation away (for SPAs)
		window.addEventListener('popstate', releaseMachine);
	</script>
</Layout>
