---
import Layout from '../layouts/Layout.astro';
import SecurityKeypad from '../components/SecurityKeypad.jsx';
---

<Layout title="Security Keypad" showHeader={false}>
    <SecurityKeypad client:load />
</Layout>

<style>
    :global(body) {
        font-family: system-ui, sans-serif;
        background: linear-gradient(145deg, #1a202c, #2d3748);
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
    }

    :global(.security-keypad) {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 30px;
    }

    :global(.keypad-container) {
        max-width: 400px;
        width: 100%;
        background: linear-gradient(145deg, #4a5568, #2d3748);
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3), 0 10px 20px rgba(0,0,0,0.2), inset 0 -2px 4px rgba(0,0,0,0.3), inset 0 2px 4px rgba(255,255,255,0.1);
    }

    :global(.keypad) {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(4, 1fr);
        gap: 15px;
        margin-bottom: 20px;
    }

    :global(.key) {
        aspect-ratio: 1;
        border: 2px solid #555;
        border-radius: 8px;
        background: linear-gradient(145deg, #333, #222);
        color: #999;
        font-size: 1.2rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.15s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0 3px 8px rgba(0,0,0,0.3);
    }

    :global(.key:hover) {
        border-color: #666;
        background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
    }

    :global(.key:active) {
        transform: scale(0.95);
        border-color: #777;
        background: linear-gradient(145deg, #222, #111);
    }

    :global(.key.pressed) {
        border-color: #4ecdc4;
        background: linear-gradient(145deg, #2a4a4a, #1a3a3a);
        box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
    }

    :global(.key:nth-child(1)) { grid-column: 1; grid-row: 1; }
    :global(.key:nth-child(2)) { grid-column: 2; grid-row: 1; }
    :global(.key:nth-child(3)) { grid-column: 3; grid-row: 1; }
    :global(.key:nth-child(4)) { grid-column: 1; grid-row: 2; }
    :global(.key:nth-child(5)) { grid-column: 2; grid-row: 2; }
    :global(.key:nth-child(6)) { grid-column: 3; grid-row: 2; }
    :global(.key:nth-child(7)) { grid-column: 1; grid-row: 3; }
    :global(.key:nth-child(8)) { grid-column: 2; grid-row: 3; }
    :global(.key:nth-child(9)) { grid-column: 3; grid-row: 3; }
    :global(.key:nth-child(10)) { grid-column: 2; grid-row: 4; }

    :global(.input-display) {
        background: #111;
        border: 2px solid #333;
        border-radius: 5px;
        padding: 15px;
        min-height: 75px;
        color: #4ecdc4;
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
    }

    :global(.success) {
        color: #4ecdc4 !important;
        animation: glow 1s ease-in-out;
    }

    @keyframes :global(glow) {
        0%, 100% { text-shadow: none; }
        50% { text-shadow: 0 0 15px #4ecdc4; }
    }

    :global(.input-display.error) {
        color: #ff6b6b !important;
        border-color: #ff6b6b;
        animation: shake 0.5s ease-in-out;
    }

    @keyframes :global(shake) {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

</style>