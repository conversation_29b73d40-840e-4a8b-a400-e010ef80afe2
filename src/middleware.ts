import { auth } from "./lib/auth";
import { defineMiddleware } from "astro:middleware";

export const onRequest = defineMiddleware(async (context, next) => {
    const sessionData = await auth.api
        .getSession({
            headers: context.request.headers,
        })

    if (sessionData) {
        context.locals.user = sessionData.user;
        context.locals.session = sessionData.session;
    } else {
        context.locals.user = null;
        context.locals.session = null;
    }

    // Protected routes - redirect to login if not authenticated
    const protectedRoutes = ['/machine', '/admin'];
    const isProtectedRoute = protectedRoutes.some(route => 
        context.url.pathname === route || context.url.pathname.startsWith(route + '/')
    );

    // Admin API routes that require admin role (using prefix-based authentication)
    const isAdminApiRoute = context.url.pathname.startsWith('/api/admin/');
    
    const isLoginPage = context.url.pathname === '/login';
    const isApiRoute = context.url.pathname.startsWith('/api/');

    // Check authentication for protected routes
    if (isProtectedRoute && !sessionData && !isLoginPage && !isApiRoute) {
        const redirectUrl = encodeURIComponent(context.url.pathname + context.url.search);
        return context.redirect(`/login?redirect=${redirectUrl}`);
    }

    // Check admin role for admin API routes
    if (isAdminApiRoute && (!sessionData?.user || sessionData.user.role !== 'admin')) {
        return new Response('Unauthorized', { status: 401 });
    }

    return next();
});
