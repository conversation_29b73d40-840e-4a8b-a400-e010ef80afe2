---
import HintPlatter from '../components/HintPlatter.jsx';

export interface Props {
	title?: string;
	showHeader?: boolean;
}

const { title = "Astro App", showHeader = true } = Astro.props;
const user = Astro.locals.user;
const session = Astro.locals.session;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
	</head>
	<body>
		{showHeader && session && (
			<header class="fixed top-0 left-0 right-0 z-50 bg-gray-800 text-white shadow-lg">
				<div class="flex justify-between items-center px-4 py-3">
					<div class="flex items-center space-x-3">
						<h1 class="text-lg font-bold">Point Zero</h1>
						{user && (
							<span class="text-sm text-gray-300">Welcome, {user.name}</span>
						)}
					</div>
					<button 
						id="logout-btn"
						class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-sm font-medium"
					>
						Logout
					</button>
				</div>
			</header>
		)}
		<main class={(showHeader && session ? "pt-16 h-full" : "h-full") + " w-screen"}>
			<slot />
		</main>

		<HintPlatter client:load />

		<script>
			import { authClient } from '../lib/auth-client';
			
			const logoutBtn = document.getElementById('logout-btn');
			if (logoutBtn) {
				logoutBtn.addEventListener('click', async () => {
					try {
						await authClient.signOut();
						window.location.href = '/login';
					} catch (error) {
						console.error('Logout error:', error);
						window.location.href = '/login';
					}
				});
			}
		</script>
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		padding: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
	}
	
	body {
		font-family: system-ui, sans-serif;
	}
</style>
