import { auth } from "../src/lib/auth";
import { nameToElements, generatePassword } from "../src/lib/chemical";

const playerData = [
  // Americas
  { name: "<PERSON><PERSON><PERSON>", role: "americas" },
  { name: "<PERSON>", role: "americas" },
  { name: "<PERSON>", role: "americas" },
  { name: "<PERSON>", role: "americas" },
  
  // Eurafrica
  { name: "<PERSON><PERSON><PERSON>", role: "eurafrica" },
  { name: "<PERSON><PERSON>", role: "eurafrica" },
  { name: "<PERSON><PERSON>", role: "eurafrica" },
  { name: "So<PERSON>", role: "eurafrica" },
  
  // O<PERSON>sia
  { name: "<PERSON>", role: "oceasia" },
  { name: "<PERSON><PERSON>", role: "oceasia" },
  { name: "<PERSON><PERSON><PERSON>", role: "oceasia" },
  { name: "<PERSON><PERSON>", role: "oceasia" },

  // { name: "<PERSON><PERSON><PERSON>", role: "eurafrica" },

  // Personne
  { name: "<PERSON><PERSON>", role: "" },

  // Admin
  { name: "Admin", role: "admin" },
];

async function createPlayers() {
  try {
    console.log("Creating player accounts...");
    
    for (const player of playerData) {
      const elementSymbols = nameToElements(player.name);
      
      let chemicalPassword: string;
      let elements: string | null = null;
      
      if (!elementSymbols && player.name.toLowerCase() !== 'admin') {
        console.log(`⚠️  Skipping ${player.name} - cannot convert to chemical elements`);
        continue;
      }

      if (player.name.toLowerCase() === 'admin') {
        chemicalPassword = 'admin';
        elements = null;
      } else {
        chemicalPassword = generatePassword(elementSymbols!);
        elements = JSON.stringify(elementSymbols);
      }

      const email = `${player.name.toLowerCase()}@vertven.com`;

      try {
        const user = await auth.api.signUpEmail({
          body: {
            email,
            password: chemicalPassword,
            name: player.name,
            elements,
            chemicalPassword: player.name.toLowerCase() === 'admin' ? 'admin' : chemicalPassword,
            mustChangePassword: player.name.toLowerCase() !== 'personne',
            role: player.role,
          },
        });

        if (user?.user) {
          console.log(`✅ Created player: ${player.name}`);
          console.log(`   Email: ${email}`);
          console.log(`   Role: ${player.role}`);
          if (elementSymbols) {
            console.log(`   Elements: ${elementSymbols.join(', ')}`);
          }
          console.log(`   Initial Password: ${chemicalPassword}`);
          console.log('');
        }
      } catch (error: any) {
        if (error.message?.includes('email already exists') || error.status === 400 || error.message?.includes('duplicate key')) {
          console.log(`⚠️  Player ${player.name} already exists`);
        } else {
          console.error(`❌ Error creating player ${player.name}:`, error.message || error);
        }
      }
    }

    console.log("Player creation complete!");
  } catch (error) {
    console.error("Error during player creation:", error);
  }
}

// Run the script
createPlayers().catch(console.error);
