// @ts-check
import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';
import node from '@astrojs/node';
import { loadEnv } from 'vite';

const { PUBLIC_ALLOWED_HOST } = loadEnv(process.env.NODE_ENV || 'development', process.cwd(), "");

// https://astro.build/config
export default defineConfig({
  integrations: [react(), tailwind()],
  output: 'server',
  adapter: node({
    mode: 'standalone'
  }),
  server: {
    allowedHosts: PUBLIC_ALLOWED_HOST ? PUBLIC_ALLOWED_HOST.split(',').map(host => host.trim()) : []
  }
});
